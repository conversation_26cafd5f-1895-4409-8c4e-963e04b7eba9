from datetime import datetime

# Global variable to store the last BANKNIFTY or FINNIFTY expiry date
last_ddmmm_expiry = None

def generate_index_symbol(index_name, expiry_date, strike_price, option_type):
    """
    Generate trading symbol for NIFTY, BANKNIFTY, or FINNIFTY options based on index name, expiry date, strike price, and option type.
    If NIFTY expiry matches the last BANKNIFTY or FINNIFTY expiry, NIFTY uses DDMMM format (e.g., 25MAY).

    Parameters:
    - index_name (str): Name of the index (e.g., "NIFTY", "BANKNIFTY", or "FINNIFTY")
    - expiry_date (str): Expiry date in format "YYYY-MM-DD"
    - strike_price (int/float): Strike price (e.g., 24200 for NIFTY, 55100 for BANKNIFTY/FINNIFTY)
    - option_type (str): Option type, either "CE" or "PE"

    Returns:
    - str: Formatted trading symbol (e.g., "NIFTY25MAY24200CE", "BANKNIFTY25MAY55100CE", or "FINNIFTY25MAY55100CE")
    """
    global last_ddmmm_expiry

    try:
        # Validate inputs
        index_name = index_name.upper()
        if index_name not in ["NIFTY", "BANKNIFTY", "FINNIFTY"]:
            raise ValueError("Index name must be 'NIFTY', 'BANKNIFTY', or 'FINNIFTY'")
        if option_type.upper() not in ["CE", "PE"]:
            raise ValueError("Option type must be 'CE' or 'PE'")

        # Parse expiry date
        expiry = datetime.strptime(expiry_date, "%Y-%m-%d")

        # Format expiry based on index
        day = f"{expiry.day:02d}"  # Day of the month as two digits (e.g., 25)
        year = str(expiry.year)[-2:]  # Last two digits of the year (e.g., 25)

        if index_name in ["BANKNIFTY", "FINNIFTY"]:
            month = expiry.strftime("%b").upper()  # Three-letter month (e.g., MAY)
            expiry_code = f"{year}{month}"
            # Update last BANKNIFTY/FINNIFTY expiry
            last_ddmmm_expiry = expiry_date
        else:  # NIFTY
            # Check if NIFTY expiry matches last BANKNIFTY/FINNIFTY expiry
            if last_ddmmm_expiry and expiry_date == last_ddmmm_expiry:
                month = expiry.strftime("%b").upper()  # Use DDMMM format (e.g., MAY)
                expiry_code = f"{year}{month}"
            else:
                month = str(expiry.month)  # Numerical month (e.g., 5 for May)
                expiry_code = f"{year}{month}{day}"

        # Format strike price (remove decimal if whole number)
        strike = int(strike_price)

        # Combine to form symbol
        symbol = f"{index_name}{expiry_code}{strike}{option_type.upper()}"
        return symbol

    except ValueError as e:
        print(f"Error: {str(e)}")
        return None
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        return None

# Example usage
if __name__ == "__main__":
    # Example inputs for BANKNIFTY
    banknifty_inputs = {
        "index": "BANKNIFTY",
        "expiry": "2025-05-29",
        "strike": 55100,
        "opt_type": "CE"
    }

    # Example inputs for FINNIFTY
    finnifty_inputs = {
        "index": "FINNIFTY",
        "expiry": "2025-05-29",
        "strike": 55100,
        "opt_type": "CE"
    }

    # Example inputs for NIFTY (same expiry as BANKNIFTY/FINNIFTY)
    nifty_same_expiry = {
        "index": "NIFTY",
        "expiry": "2025-05-29",
        "strike": 24200,
        "opt_type": "CE"
    }

    # Example inputs for NIFTY (different expiry)
    nifty_diff_expiry = {
        "index": "NIFTY",
        "expiry": "2025-05-08",
        "strike": 24200,
        "opt_type": "CE"
    }

    # Generate symbols
    for inputs in [banknifty_inputs, finnifty_inputs, nifty_same_expiry, nifty_diff_expiry]:
        symbol = generate_index_symbol(
            inputs["index"],
            inputs["expiry"],
            inputs["strike"],
            inputs["opt_type"]
        )
        if symbol:
            print(f"Generated trading symbol: {symbol}")