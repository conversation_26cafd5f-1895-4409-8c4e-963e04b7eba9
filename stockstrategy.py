import os
import json
import copy
import pandas as pd
import numpy as np
from kiteconnect import KiteConnect, KiteTicker
import time
import dateutil.parser
import threading
import sys
import requests
import urllib
import datetime
import configparser
import pymysql
import asyncio
import logging
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("stockstrategy.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
INVESTMENT_AMOUNT = 100000  # ₹100,000 per trade (configurable)
SUPPORT_BUFFER = 0.005  # 0.5% buffer for support
RESISTANCE_BUFFER = 0.01  # 1% buffer for resistance
STOP_LOSS_PERCENT = 0.015  # 1.5% stop loss
FILTER_UPDATE_INTERVAL = 600  # 10 minutes in seconds
DATA_COLLECTION_INTERVAL = 1200  # 20 minutes in seconds

# Market hours
MARKET_START_TIME = datetime.time(9, 15)  # Market opens at 9:15 AM
MARKET_END_TIME = datetime.time(15, 30)  # Market closes at 3:30 PM

class StockStrategy:
    def __init__(self):
        """Initialize the Stock Strategy with database connection and Zerodha API."""
        self.db_config = self._get_db_config()
        self.conn = None
        self.cursor = None
        self._connect_to_db()
        
        # Initialize Zerodha API
        self._initialize_zerodha_api()
        
        # Trading state
        self.filtered_stocks = []
        self.positions = {}
        self.tick_data = {}
        self.token_symbol = {}
        self.kws = None
        
        # Create required tables
        self._create_tables()
        
        # Initialize websocket
        self._start_websocket()
        
        logger.info("StockStrategy initialized successfully")

    def _get_db_config(self):
        """Get MySQL connection details from config file."""
        config = configparser.ConfigParser()
        if os.path.exists('db_config.ini'):
            config.read('db_config.ini')
            return config['mysql']
        else:
            return {
                'host': 'localhost',
                'port': '3306',
                'user': 'root',
                'password': 'vinayak123',
                'database': 'option_chain_data'
            }

    def _connect_to_db(self):
        """Connect to MySQL database."""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=int(self.db_config['port']),
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            self.cursor = self.conn.cursor()
            logger.info("Connected to MySQL database")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    def _initialize_zerodha_api(self):
        """Initialize Zerodha API connection."""
        try:
            # Import and use existing Zerodha API setup
            from zerodhaapi import KiteApp, get_access_token
            
            self.enctoken = get_access_token()
            self.kite = KiteApp(enctoken=self.enctoken)
            user_profile = self.kite.profile()
            logger.info(f"Connected to Zerodha as {user_profile['user_id']} ({user_profile['user_name']})")
            
            # Get exchange data
            self.exchange_data = self.kite.instruments()
            logger.info(f"Fetched {len(self.exchange_data)} instruments from Zerodha")
        except Exception as e:
            logger.error(f"Failed to initialize Zerodha API: {e}")
            raise

    def _create_tables(self):
        """Create required database tables."""
        try:
            # Create live_filtered_stocks table
            self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS live_filtered_stocks (
                id INT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(50),
                ltp FLOAT,
                timestamp DATETIME,
                max_put_oi_strike FLOAT,
                max_call_oi_strike FLOAT,
                UNIQUE KEY unique_symbol (symbol)
            )
            """)
            
            # Create live_stock_trades table
            self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS live_stock_trades (
                trade_id INT AUTO_INCREMENT PRIMARY KEY,
                symbol VARCHAR(50),
                entry_price FLOAT,
                quantity INT,
                entry_time DATETIME,
                status VARCHAR(20),
                support_level FLOAT,
                resistance_level FLOAT,
                investment_amount FLOAT,
                stop_loss_price FLOAT,
                target_price FLOAT,
                exit_price FLOAT,
                exit_time DATETIME,
                pnl FLOAT
            )
            """)
            
            # Ensure orders_log table exists (reuse existing structure)
            self.cursor.execute("SHOW TABLES LIKE 'orders_log'")
            if not self.cursor.fetchone():
                self.cursor.execute("""
                CREATE TABLE orders_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    timestamp DATETIME,
                    symbol VARCHAR(50),
                    order_type VARCHAR(10),
                    option_type VARCHAR(5),
                    strike FLOAT,
                    spot_price FLOAT,
                    option_price FLOAT,
                    reason VARCHAR(255),
                    support FLOAT,
                    resistance FLOAT,
                    pnl FLOAT,
                    status VARCHAR(20)
                )
                """)
            
            self.conn.commit()
            logger.info("Database tables created/verified successfully")
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            raise

    def _start_websocket(self):
        """Start Zerodha websocket for real-time data."""
        try:
            # Use existing websocket setup pattern
            login_credential = {"api_key": "TradeViaPython"}  # Use existing pattern
            access_token = self.enctoken + "&user_id=" + self.kite.profile()["user_id"]
            
            self.kws = KiteTicker(api_key=login_credential["api_key"], access_token=access_token)
            
            def on_ticks(ws, ticks):
                for tick in ticks:
                    if tick["instrument_token"] in self.token_symbol:
                        symbol = self.token_symbol[tick["instrument_token"]]
                        self.tick_data[symbol] = tick
            
            self.kws.on_ticks = on_ticks
            self.kws.connect(threaded=True)
            
            while not self.kws.is_connected():
                time.sleep(1)
            
            logger.info("WebSocket connected successfully")
        except Exception as e:
            logger.error(f"Failed to start websocket: {e}")
            raise

    def is_market_open(self):
        """Check if market is currently open."""
        now = datetime.datetime.now().time()
        return MARKET_START_TIME <= now <= MARKET_END_TIME

    def filter_stocks(self):
        """Filter stocks based on specified criteria."""
        try:
            # Query option_data_need_live_stocks table
            self.cursor.execute("""
                SELECT symbol, Spot_LTP, Max_Put_OI_Strike, Max_Call_OI_Strike
                FROM option_data_need_live_stocks
                WHERE Spot_LTP > 100
            """)
            
            stocks_data = self.cursor.fetchall()
            filtered_stocks = []
            
            for symbol, ltp, max_put_oi_strike, max_call_oi_strike in stocks_data:
                if max_put_oi_strike and max_call_oi_strike:
                    # Check if LTP is within 5% range of max_put_oi_strike
                    lower_bound = 0.95 * max_put_oi_strike
                    upper_bound = 1.05 * max_put_oi_strike
                    
                    if lower_bound <= ltp <= upper_bound:
                        # Check if max_call_oi_strike <= max_put_oi_strike + 7%
                        max_allowed_call_strike = max_put_oi_strike * 1.07
                        
                        if max_call_oi_strike <= max_allowed_call_strike:
                            filtered_stocks.append({
                                'symbol': symbol,
                                'ltp': ltp,
                                'max_put_oi_strike': max_put_oi_strike,
                                'max_call_oi_strike': max_call_oi_strike
                            })
            
            self.filtered_stocks = filtered_stocks
            logger.info(f"Filtered {len(filtered_stocks)} stocks: {[s['symbol'] for s in filtered_stocks]}")
            
            return filtered_stocks
        except Exception as e:
            logger.error(f"Error filtering stocks: {e}")
            return []

    def subscribe_to_live_data(self):
        """Subscribe to live LTP updates for filtered stocks."""
        try:
            if not self.filtered_stocks:
                return
            
            # Clear previous subscriptions
            if self.token_symbol:
                self.kws.unsubscribe(list(self.token_symbol.keys()))
                self.token_symbol = {}
                self.tick_data = {}
            
            # Subscribe to filtered stocks
            tokens_to_subscribe = []
            
            for stock in self.filtered_stocks:
                symbol = stock['symbol']
                
                # Find instrument token for the stock
                for instrument in self.exchange_data:
                    if (instrument['tradingsymbol'] == symbol and 
                        instrument['exchange'] == 'NSE' and 
                        instrument['instrument_type'] == 'EQ'):
                        
                        token = instrument['instrument_token']
                        self.token_symbol[token] = symbol
                        tokens_to_subscribe.append(token)
                        break
            
            if tokens_to_subscribe:
                self.kws.subscribe(tokens_to_subscribe)
                self.kws.set_mode(self.kws.MODE_FULL, tokens_to_subscribe)
                logger.info(f"Subscribed to {len(tokens_to_subscribe)} stock tokens")
            
        except Exception as e:
            logger.error(f"Error subscribing to live data: {e}")

    def update_live_filtered_stocks(self):
        """Update live_filtered_stocks table with current data."""
        try:
            for stock in self.filtered_stocks:
                symbol = stock['symbol']
                current_ltp = self.tick_data.get(symbol, {}).get('last_price', stock['ltp'])
                
                # Update or insert into live_filtered_stocks table
                self.cursor.execute("""
                    INSERT INTO live_filtered_stocks 
                    (symbol, ltp, timestamp, max_put_oi_strike, max_call_oi_strike)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    ltp = VALUES(ltp),
                    timestamp = VALUES(timestamp)
                """, (
                    symbol,
                    current_ltp,
                    datetime.datetime.now(),
                    stock['max_put_oi_strike'],
                    stock['max_call_oi_strike']
                ))
            
            self.conn.commit()
        except Exception as e:
            logger.error(f"Error updating live filtered stocks: {e}")

    def check_entry_conditions(self):
        """Check for entry conditions and place trades."""
        try:
            for stock in self.filtered_stocks:
                symbol = stock['symbol']

                # Skip if already have position for this stock
                if symbol in self.positions and self.positions[symbol]['status'] == 'ACTIVE':
                    continue

                current_ltp = self.tick_data.get(symbol, {}).get('last_price')
                if not current_ltp:
                    continue

                support = stock['max_put_oi_strike']
                resistance = stock['max_call_oi_strike']

                # Calculate entry level (support + 0.5% buffer)
                entry_level = support * (1 + SUPPORT_BUFFER)

                # Check if LTP crosses above entry level from below
                # For simplicity, we'll check if current LTP is above entry level
                if current_ltp > entry_level:
                    self._place_buy_order(symbol, current_ltp, support, resistance)

        except Exception as e:
            logger.error(f"Error checking entry conditions: {e}")

    def _place_buy_order(self, symbol, current_ltp, support, resistance):
        """Place a buy order for the stock."""
        try:
            # Calculate quantity based on investment amount
            quantity = int(INVESTMENT_AMOUNT / current_ltp)

            if quantity <= 0:
                logger.warning(f"Quantity is 0 for {symbol} at price {current_ltp}")
                return

            # Calculate stop loss and target prices
            stop_loss_price = current_ltp * (1 - STOP_LOSS_PERCENT)
            target_price = resistance * (1 - RESISTANCE_BUFFER)

            # For simulation, we'll assume the order is executed at current LTP
            # In real implementation, you would place actual order through Zerodha API

            # Record the position
            self.positions[symbol] = {
                'status': 'ACTIVE',
                'entry_price': current_ltp,
                'quantity': quantity,
                'entry_time': datetime.datetime.now(),
                'support_level': support,
                'resistance_level': resistance,
                'stop_loss_price': stop_loss_price,
                'target_price': target_price,
                'investment_amount': quantity * current_ltp
            }

            # Insert into live_stock_trades table
            self.cursor.execute("""
                INSERT INTO live_stock_trades
                (symbol, entry_price, quantity, entry_time, status, support_level,
                 resistance_level, investment_amount, stop_loss_price, target_price)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                symbol, current_ltp, quantity, datetime.datetime.now(), 'ACTIVE',
                support, resistance, quantity * current_ltp, stop_loss_price, target_price
            ))

            # Log the order
            self.cursor.execute("""
                INSERT INTO orders_log
                (timestamp, symbol, order_type, option_type, strike, spot_price,
                 option_price, reason, support, resistance, status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                datetime.datetime.now(), symbol, 'BUY', 'EQ', 0, current_ltp,
                current_ltp, f'Support crossed from below at {support + SUPPORT_BUFFER:.2%}',
                support, resistance, 'EXECUTED'
            ))

            self.conn.commit()
            logger.info(f"BUY order placed for {symbol} at {current_ltp}, quantity: {quantity}")

        except Exception as e:
            logger.error(f"Error placing buy order for {symbol}: {e}")

    def check_exit_conditions(self):
        """Check for exit conditions and close positions."""
        try:
            for symbol, position in list(self.positions.items()):
                if position['status'] != 'ACTIVE':
                    continue

                current_ltp = self.tick_data.get(symbol, {}).get('last_price')
                if not current_ltp:
                    continue

                should_exit = False
                exit_reason = ""

                # Check stop loss
                if current_ltp <= position['stop_loss_price']:
                    should_exit = True
                    exit_reason = f"Stop loss hit at {current_ltp}"

                # Check target (resistance - 1% buffer)
                elif current_ltp <= position['target_price']:
                    should_exit = True
                    exit_reason = f"Target reached at {current_ltp}"

                # Check month end (auto square-off)
                elif self._is_month_end():
                    should_exit = True
                    exit_reason = "Month end auto square-off"

                if should_exit:
                    self._place_sell_order(symbol, current_ltp, exit_reason)

        except Exception as e:
            logger.error(f"Error checking exit conditions: {e}")

    def _place_sell_order(self, symbol, current_ltp, reason):
        """Place a sell order to close position."""
        try:
            position = self.positions[symbol]

            # Calculate P&L
            pnl = (current_ltp - position['entry_price']) * position['quantity']

            # Update position status
            self.positions[symbol]['status'] = 'CLOSED'

            # Update live_stock_trades table
            self.cursor.execute("""
                UPDATE live_stock_trades
                SET status = 'CLOSED', exit_price = %s, exit_time = %s, pnl = %s
                WHERE symbol = %s AND status = 'ACTIVE'
            """, (current_ltp, datetime.datetime.now(), pnl, symbol))

            # Log the sell order
            self.cursor.execute("""
                INSERT INTO orders_log
                (timestamp, symbol, order_type, option_type, strike, spot_price,
                 option_price, reason, support, resistance, pnl, status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                datetime.datetime.now(), symbol, 'SELL', 'EQ', 0, current_ltp,
                current_ltp, reason, position['support_level'],
                position['resistance_level'], pnl, 'EXECUTED'
            ))

            self.conn.commit()
            logger.info(f"SELL order placed for {symbol} at {current_ltp}, P&L: {pnl:.2f}, Reason: {reason}")

        except Exception as e:
            logger.error(f"Error placing sell order for {symbol}: {e}")

    def _is_month_end(self):
        """Check if it's month end."""
        today = datetime.date.today()
        next_month = today.replace(day=28) + datetime.timedelta(days=4)
        last_day_of_month = next_month - datetime.timedelta(days=next_month.day)
        return today == last_day_of_month

    async def run_strategy(self):
        """Main strategy execution loop."""
        logger.info("Starting Stock Strategy execution")

        last_filter_update = 0
        last_data_collection = 0

        while True:
            try:
                # Check if market is open
                if not self.is_market_open():
                    current_time = datetime.datetime.now().strftime("%H:%M:%S")
                    logger.info(f"[{current_time}] Market is closed. Waiting for market hours...")
                    await asyncio.sleep(60)  # Check every minute when market is closed
                    continue

                current_time = time.time()

                # Update filtered stocks every 10 minutes
                if current_time - last_filter_update >= FILTER_UPDATE_INTERVAL:
                    self.filter_stocks()
                    self.subscribe_to_live_data()
                    last_filter_update = current_time

                # Collect data every 20 minutes (for filtering)
                if current_time - last_data_collection >= DATA_COLLECTION_INTERVAL:
                    # This would trigger data collection from option_data_need_live_stocks
                    # The actual data collection is handled by OC_Stocks.py
                    last_data_collection = current_time

                # Update live data every second
                self.update_live_filtered_stocks()

                # Check trading conditions every second
                self.check_entry_conditions()
                self.check_exit_conditions()

                # Sleep for 1 second before next iteration
                await asyncio.sleep(1)

            except KeyboardInterrupt:
                logger.info("Strategy stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in strategy execution: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    def cleanup(self):
        """Cleanup resources."""
        try:
            if self.kws and self.kws.is_connected():
                self.kws.close()
            if self.conn:
                self.conn.close()
            logger.info("Cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Main function to run the stock strategy."""
    strategy = None
    try:
        strategy = StockStrategy()
        asyncio.run(strategy.run_strategy())
    except KeyboardInterrupt:
        logger.info("Strategy interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
    finally:
        if strategy:
            strategy.cleanup()

if __name__ == "__main__":
    main()
