# Option Chain Data Viewer

A scalable web-based frontend for viewing option chain data from multiple symbols collected by the OC_Nifty.py script.

## Features

- **Live Data View**: Real-time display of option chain data with auto-refresh every second
- **Historical Data View**: View historical data by date and time
- **Multi-Symbol Support**: Handles data from multiple instances of OC_Nifty.py running for different symbols
- **Responsive Design**: Works on desktop and mobile devices
- **Summary Cards**: Quick view of important metrics like Spot LTP, PCR, Max Pain, etc.
- **ATM Strike Highlighting**: Automatically highlights the At-The-Money strike price

## Technical Details

### Backend

- **Flask**: Lightweight web framework for Python
- **SQLite**: Database for storing option chain data
- **Pandas**: Data manipulation and analysis

### Frontend

- **Bootstrap 5**: Responsive UI framework
- **Chart.js**: For data visualization (future enhancement)
- **JavaScript**: Dynamic content loading and auto-refresh

## Setup and Usage

1. **Install Dependencies**:
   ```
   pip install flask pandas
   ```

2. **Run the Application**:
   ```
   python app.py
   ```

3. **Access the Web Interface**:
   Open a browser and navigate to `http://localhost:5000`

## Database Structure

The application reads from two tables in the SQLite database:

1. **option_data_need**: Contains summary data for each symbol
   - Spot price, futures price, VIX, OI totals, PCR, etc.

2. **option_data**: Contains detailed option chain data
   - Strike prices, call/put data, OI, IV, etc.

## Running Multiple Instances

You can run multiple instances of OC_Nifty.py for different symbols:

1. Start the first instance:
   ```
   python OC_Nifty.py
   ```

2. Start additional instances in separate terminals:
   ```
   python OC_Nifty.py
   ```

3. For each instance, select a different symbol when prompted.

The frontend will automatically detect all symbols in the database and allow you to switch between them.

## Customization

- **Auto-refresh Rate**: You can modify the auto-refresh rate in the JavaScript code (currently set to 1 second)
- **UI Theme**: The application uses Bootstrap, so you can easily customize the appearance

## Future Enhancements

- Add charts for visualizing price and OI trends
- Add export functionality for data
- Implement user authentication
- Add alerts for significant changes in metrics
