# MySQL Migration Guide

This guide explains how to use the stock trading application with MySQL instead of SQLite.

## Setup

1. **Install Required Packages**:
   ```
   pip install pymysql cryptography
   ```

2. **Create MySQL Database**:
   ```
   python create_mysql_tables.py
   ```
   This will create the necessary tables in your MySQL database.

3. **Configure MySQL Connection**:
   Create a file named `db_config.ini` with the following content:
   ```ini
   [mysql]
   host = localhost
   port = 3306
   user = root
   password = your_password
   database = option_chain_data
   ```
   Replace `your_password` with your MySQL password.

## Running the Application

1. **Run the Data Collection Scripts**:
   ```
   python OC_Nifty.py
   ```
   or
   ```
   python OC_Nifty copy.py
   ```
   or
   ```
   python OC_Niftyfin.py
   ```
   These will collect data and store it in the MySQL database.

2. **Run the Web Application**:
   ```
   python app.py
   ```
   The web application will now use MySQL to retrieve and display data.

## Troubleshooting

If you encounter any issues:

1. **Check MySQL Connection**:
   ```
   python test_mysql.py
   ```
   This will verify that the connection to MySQL is working correctly.

2. **Check MySQL Data**:
   ```
   python check_mysql_data.py
   ```
   This will show the data in the MySQL database.

3. **Test Data Insertion**:
   ```
   python test_updated_insertion.py
   ```
   This will test inserting data into the MySQL database.

## Common Issues

1. **Unknown Column Error**:
   If you see an error like `Unknown column 'CE_LTQ' in 'field list'`, run:
   ```
   python fix_option_data_table.py
   ```
   This will recreate the option_data table with all necessary columns.

2. **Connection Error**:
   If you see a connection error, check that:
   - MySQL server is running
   - Connection details in `db_config.ini` are correct
   - The database exists

3. **Data Not Being Inserted**:
   If data is not being inserted, check the console output for error messages.
   The improved error handling will show detailed error messages.

## Benefits of Using MySQL

1. **Better Performance**: MySQL is designed to handle larger datasets and concurrent connections more efficiently than SQLite.

2. **Multi-User Access**: Unlike SQLite, MySQL allows multiple users to access the database simultaneously.

3. **Scalability**: MySQL can handle much larger datasets and is better suited for applications that need to scale.

4. **Network Access**: MySQL can be accessed over a network, allowing you to separate your database server from your application server.

5. **Better Security**: MySQL provides more robust security features, including user authentication and access control.
