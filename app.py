import pandas as pd
import numpy as np
from flask import Flask, render_template, jsonify, request
import datetime
import json
import os
import configparser
import pymysql
from sqlalchemy import create_engine

app = Flask(__name__)

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_data'
        }

# Function to get data from the database
def get_data_from_db(query, params=()):
    mysql_config = get_mysql_config()

    try:
        # Create connection
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )

        # Convert SQLite-style placeholders (?) to MySQL-style placeholders (%s)
        query = query.replace('?', '%s')

        # Execute query
        df = pd.read_sql_query(query, conn, params=params)

        # Close connection
        conn.close()

        return df
    except Exception as e:
        print(f"Database error: {e}")
        return pd.DataFrame()

# Function to extract symbol from timestamp
def extract_symbol(timestamp):
    parts = timestamp.split()
    if len(parts) > 3 and parts[3] == "symbol":  # Format: "YYYY-MM-DD HH:MM:SS Symbol symbol"
        return parts[2]
    return "Unknown"

# Home page
@app.route('/')
def index():
    return render_template('index.html')

# Function to extract symbols from database
def extract_symbols_from_db():
    # Try to get symbols directly from the live tables first
    query = "SELECT DISTINCT symbol FROM option_data_need_live WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Try to get symbols from option_data_live table
    query = "SELECT DISTINCT symbol FROM option_data_live WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Try to get symbols from historical tables
    query = "SELECT DISTINCT symbol FROM option_data_need WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Try to get symbols from option_data table
    query = "SELECT DISTINCT symbol FROM option_data WHERE symbol IS NOT NULL"
    df = get_data_from_db(query)

    if not df.empty and 'symbol' in df.columns:
        symbols = set(df['symbol'].dropna().unique())
        if symbols:
            return list(symbols)

    # Fallback: Common symbols to look for in timestamps
    common_symbols = ['NIFTY', 'BANKNIFTY', 'FINNIFTY', 'SENSEX', 'MIDCPNIFTY']
    symbols = set()

    # Get all unique timestamps
    query = "SELECT DISTINCT timestamp FROM option_data_need"
    df = get_data_from_db(query)

    # Try to extract symbols from timestamps
    if not df.empty and 'timestamp' in df.columns:
        for timestamp in df['timestamp']:
            if timestamp and isinstance(timestamp, str):
                parts = timestamp.split()
                if len(parts) > 2:
                    potential_symbol = parts[2]
                    if potential_symbol in common_symbols:
                        symbols.add(potential_symbol)

    # If no symbols found, try to infer from strike prices
    if not symbols:
        query = "SELECT DISTINCT Strike FROM option_data"
        strikes_df = get_data_from_db(query)

        # Convert strikes to float where possible
        strikes = []
        if not strikes_df.empty and 'Strike' in strikes_df.columns:
            for s in strikes_df['Strike']:
                try:
                    strikes.append(float(s))
                except (ValueError, TypeError):
                    pass

            # Nifty is typically around 15000-25000, BankNifty around 30000-50000
            if any(15000 <= s <= 25000 for s in strikes):
                symbols.add('NIFTY')
            if any(30000 <= s <= 50000 for s in strikes):
                symbols.add('BANKNIFTY')

    # If still no symbols found, use default list
    if not symbols:
        symbols = {'NIFTY', 'BANKNIFTY', 'FINNIFTY'}

    return list(symbols)

# API to get available symbols
@app.route('/api/symbols')
def get_symbols():
    symbols = extract_symbols_from_db()
    return jsonify(symbols)

# API to get latest data for a specific symbol
@app.route('/api/latest/<symbol>')
def get_latest_data(symbol):
    # Get latest data from option_data_need_live for the specified symbol
    query = "SELECT * FROM option_data_need_live WHERE symbol = ? LIMIT 1"
    df_need = get_data_from_db(query, (symbol,))

    # If no data found in live table, try the historical table
    if df_need.empty:
        query = "SELECT * FROM option_data_need WHERE symbol = ? ORDER BY timestamp DESC LIMIT 1"
        df_need = get_data_from_db(query, (symbol,))

        # If still no data found for this symbol, try getting the latest data regardless of symbol
        if df_need.empty:
            query = "SELECT * FROM option_data_need_live LIMIT 1"
            df_need = get_data_from_db(query)

            if df_need.empty:
                query = "SELECT * FROM option_data_need ORDER BY timestamp DESC LIMIT 1"
                df_need = get_data_from_db(query)

                if df_need.empty:
                    return jsonify({"error": "No data found"})

    # Convert to dictionary
    data_need = df_need.to_dict('records')[0]

    # Ensure symbol is set correctly
    data_need['symbol'] = symbol

    # Get option data from option_data_live for this symbol
    query = "SELECT * FROM option_data_live WHERE symbol = ? ORDER BY Strike"
    df_option = get_data_from_db(query, (symbol,))

    # If no data in live table, try getting from historical table
    if df_option.empty:
        # Get the latest timestamp for this symbol from historical table
        query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data WHERE symbol = ?"
        latest_ts_df = get_data_from_db(query, (symbol,))

        if latest_ts_df.empty or pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
            # Try getting data for any symbol from live table
            query = "SELECT * FROM option_data_live ORDER BY Strike LIMIT 1"
            df_option = get_data_from_db(query)

            if df_option.empty:
                return jsonify({
                    "error": "No option data found for this symbol"
                })
        else:
            latest_timestamp = latest_ts_df['latest_timestamp'].iloc[0]
            # Get all strikes for the latest timestamp from historical table
            query = "SELECT * FROM option_data WHERE symbol = ? AND timestamp = ? ORDER BY Strike"
            df_option = get_data_from_db(query, (symbol, latest_timestamp))

            # If still no data, try any symbol
            if df_option.empty:
                query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data"
                latest_ts_df = get_data_from_db(query)

                if latest_ts_df.empty or pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
                    return jsonify({
                        "error": "No option data found"
                    })

                latest_timestamp = latest_ts_df['latest_timestamp'].iloc[0]
                query = "SELECT * FROM option_data WHERE timestamp = ? ORDER BY Strike"
                df_option = get_data_from_db(query, (latest_timestamp,))

    # Get the latest timestamp from the data
    if 'timestamp' in df_option.columns and not df_option.empty:
        latest_timestamp = df_option['timestamp'].iloc[0]
    else:
        latest_timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Extract strike prices from the Strike column
    option_data = []
    for _, row in df_option.iterrows():
        strike = row['Strike']
        option_data.append({
            'strike': strike,
            'data': row.to_dict()
        })

    # Sort by strike price
    option_data.sort(key=lambda x: float(x['strike']) if x['strike'] != "Unknown" else 0)

    return jsonify({
        "summary": data_need,
        "option_chain": option_data,
        "timestamp": latest_timestamp
    })

# API to get historical data
@app.route('/api/historical')
def get_historical_data():
    symbol = request.args.get('symbol', '')
    date = request.args.get('date', datetime.datetime.now().strftime('%Y-%m-%d'))
    time = request.args.get('time', '')

    # Get historical data from option_data_need for the specified symbol and date
    query = "SELECT * FROM option_data_need WHERE symbol = ? AND timestamp LIKE ? ORDER BY timestamp DESC"
    df_need = get_data_from_db(query, (symbol, f'{date}%'))

    # If no data found for this symbol and date, try getting data for just the date
    if df_need.empty:
        query = "SELECT * FROM option_data_need WHERE timestamp LIKE ? ORDER BY timestamp DESC"
        df_need = get_data_from_db(query, (f'{date}%',))

        if df_need.empty:
            return jsonify({"error": "No historical data found for this date"})

    # Ensure symbol is set correctly in each record
    for i in range(len(df_need)):
        df_need.at[i, 'symbol'] = symbol

    # Convert to dictionary
    data_need = df_need.to_dict('records')

    # Get all timestamps for the specified date to populate the time dropdown
    query = "SELECT DISTINCT timestamp FROM option_data WHERE symbol = ? AND timestamp LIKE ? ORDER BY timestamp DESC"
    timestamps_df = get_data_from_db(query, (symbol, f'{date}%'))

    timestamps = []
    if not timestamps_df.empty:
        timestamps = timestamps_df['timestamp'].tolist()

    # If a specific time is provided, get data for that time
    if time:
        full_timestamp = f"{date} {time}"
        query = "SELECT * FROM option_data WHERE symbol = ? AND timestamp = ? ORDER BY Strike"
        df_option = get_data_from_db(query, (symbol, full_timestamp))
    else:
        # Otherwise, get all data for the date
        query = "SELECT * FROM option_data WHERE symbol = ? AND timestamp LIKE ? ORDER BY timestamp DESC, Strike"
        df_option = get_data_from_db(query, (symbol, f'{date}%'))

    # If no option data found for this symbol and date, try getting data for just the date
    if df_option.empty:
        if time:
            full_timestamp = f"{date} {time}"
            query = "SELECT * FROM option_data WHERE timestamp = ? ORDER BY Strike"
            df_option = get_data_from_db(query, (full_timestamp,))
        else:
            query = "SELECT * FROM option_data WHERE timestamp LIKE ? ORDER BY timestamp DESC, Strike"
            df_option = get_data_from_db(query, (f'{date}%',))

    # Group by timestamp
    option_data = {}
    for timestamp in df_option['timestamp'].unique():
        time_parts = timestamp.split()
        if len(time_parts) >= 2:
            time_key = time_parts[0] + " " + time_parts[1]  # YYYY-MM-DD HH:MM:SS
        else:
            time_key = timestamp  # Use the full timestamp if it can't be split

        if time_key not in option_data:
            option_data[time_key] = []

        # Get all rows with this timestamp
        rows = df_option[df_option['timestamp'] == timestamp]
        for _, row in rows.iterrows():
            strike = row['Strike']
            option_data[time_key].append({
                'strike': strike,
                'data': row.to_dict()
            })

    # Sort each time's data by strike price
    for time_key in option_data:
        option_data[time_key].sort(key=lambda x: float(x['strike']) if x['strike'] != "Unknown" else 0)

    return jsonify({
        "summary": data_need,
        "option_chain": option_data,
        "timestamps": timestamps
    })

# API to get available dates
@app.route('/api/dates')
def get_dates():
    symbol = request.args.get('symbol', '')

    # If symbol is provided, get dates for that symbol
    if symbol:
        query = "SELECT DISTINCT timestamp FROM option_data WHERE symbol = ? ORDER BY timestamp DESC"
        df = get_data_from_db(query, (symbol,))
    else:
        # Get all dates regardless of symbol
        query = "SELECT DISTINCT timestamp FROM option_data ORDER BY timestamp DESC"
        df = get_data_from_db(query)

    dates = []
    if not df.empty and 'timestamp' in df.columns:
        for timestamp in df['timestamp']:
            if timestamp and isinstance(timestamp, str):
                try:
                    date = timestamp.split()[0]  # YYYY-MM-DD
                    if date not in dates:
                        dates.append(date)
                except Exception:
                    pass

    # If no dates found, use today's date
    if not dates:
        dates = [datetime.datetime.now().strftime('%Y-%m-%d')]

    return jsonify(dates)

# API to get OI data for all symbols
@app.route('/api/oi-data')
def get_oi_data():
    # Get all symbols
    symbols = extract_symbols_from_db()

    # Get latest data for each symbol
    result = []

    # Process each symbol
    for symbol in symbols:
        # Get the latest data for this symbol from live table
        query = "SELECT * FROM option_data_need_live WHERE symbol = ? LIMIT 1"
        df_symbol = get_data_from_db(query, (symbol,))

        if not df_symbol.empty:
            # Use the data for this symbol from live table
            symbol_data = df_symbol.iloc[0].to_dict()

            # Get the latest timestamp for option data from live table
            query = "SELECT timestamp FROM option_data_live WHERE symbol = ? LIMIT 1"
            latest_ts_df = get_data_from_db(query, (symbol,))

            if not latest_ts_df.empty and not pd.isna(latest_ts_df['timestamp'].iloc[0]):
                symbol_data['latest_option_timestamp'] = latest_ts_df['timestamp'].iloc[0]

            result.append(symbol_data)
        else:
            # If no data in live table, try historical table
            query = "SELECT * FROM option_data_need WHERE symbol = ? ORDER BY timestamp DESC LIMIT 1"
            df_symbol = get_data_from_db(query, (symbol,))

            if not df_symbol.empty:
                # Use the data for this symbol from historical table
                symbol_data = df_symbol.iloc[0].to_dict()

                # Get the latest timestamp for option data from historical table
                query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data WHERE symbol = ?"
                latest_ts_df = get_data_from_db(query, (symbol,))

                if not latest_ts_df.empty and not pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
                    symbol_data['latest_option_timestamp'] = latest_ts_df['latest_timestamp'].iloc[0]

                result.append(symbol_data)
            else:
                # If no data found for this symbol in either table, try getting the latest data from live table
                query = "SELECT * FROM option_data_need_live LIMIT 1"
                df_latest = get_data_from_db(query)

                if not df_latest.empty:
                    # Use the latest data but set the symbol
                    symbol_data = df_latest.iloc[0].to_dict()
                    symbol_data['symbol'] = symbol

                    # Get the latest timestamp for option data
                    query = "SELECT timestamp FROM option_data_live WHERE symbol = ? LIMIT 1"
                    latest_ts_df = get_data_from_db(query, (symbol,))

                    if not latest_ts_df.empty and not pd.isna(latest_ts_df['timestamp'].iloc[0]):
                        symbol_data['latest_option_timestamp'] = latest_ts_df['timestamp'].iloc[0]

                    result.append(symbol_data)
                else:
                    # If no data in live table, try historical table
                    query = "SELECT * FROM option_data_need ORDER BY timestamp DESC LIMIT 1"
                    df_latest = get_data_from_db(query)

                    if not df_latest.empty:
                        # Use the latest data but set the symbol
                        symbol_data = df_latest.iloc[0].to_dict()
                        symbol_data['symbol'] = symbol

                        # Get the latest timestamp for option data
                        query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data WHERE symbol = ?"
                        latest_ts_df = get_data_from_db(query, (symbol,))

                        if not latest_ts_df.empty and not pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
                            symbol_data['latest_option_timestamp'] = latest_ts_df['latest_timestamp'].iloc[0]

                        result.append(symbol_data)

    # If still no data, create dummy data for each symbol
    if not result:
        for symbol in symbols:
            result.append({
                'symbol': symbol,
                'FUT_OI': 0,
                'FUT_Change_in_OI': 0,
                'Max_Call_OI': 0,
                'Max_Put_OI': 0,
                'Max_Call_OI_Strike': 0,
                'Max_Put_OI_Strike': 0,
                'Max_Call_Change_in_OI': 0,
                'Max_Put_Change_in_OI': 0,
                'Max_Call_Change_in_OI_Strike': 0,
                'Max_Put_Change_in_OI_Strike': 0,
                'Spot_LTP': 0,
                'timestamp': 'No data available',
                'latest_option_timestamp': None
            })

    return jsonify(result)

# Function to get a database connection
def get_db_connection():
    mysql_config = get_mysql_config()
    return pymysql.connect(
        host=mysql_config['host'],
        port=int(mysql_config['port']),
        user=mysql_config['user'],
        password=mysql_config['password'],
        database=mysql_config['database'],
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

# Ensure ORDER_LOG table exists
def ensure_order_log_table_exists():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Check if ORDER_LOG table exists
        cursor.execute("SHOW TABLES LIKE 'ORDER_LOG'")
        table_exists = cursor.fetchone()

        if not table_exists:
            print("Creating ORDER_LOG table...")
            cursor.execute("""
            CREATE TABLE ORDER_LOG (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME,
                symbol VARCHAR(50),
                order_type VARCHAR(10),  -- BUY or SELL
                option_type VARCHAR(5),   -- CE or PE
                strike FLOAT,
                spot_price FLOAT,
                option_price FLOAT,
                reason VARCHAR(255),      -- Reason for the trade
                support FLOAT,            -- Support level at time of trade
                resistance FLOAT,         -- Resistance level at time of trade
                pnl FLOAT,               -- P&L for the trade (NULL for BUY orders)
                status VARCHAR(20)        -- EXECUTED, FAILED, etc.
            )
            """)
            conn.commit()
            print("ORDER_LOG table created successfully")

            # Insert some sample data for testing
            cursor.execute("""
            INSERT INTO ORDER_LOG (timestamp, symbol, order_type, option_type, strike, spot_price, option_price, reason, support, resistance, status)
            VALUES (NOW(), 'NIFTY', 'BUY', 'CE', 22000, 22050.5, 150.25, 'Support crossed from below to above', 21900, 22100, 'EXECUTED')
            """)
            conn.commit()
            print("Sample data inserted into ORDER_LOG table")
        else:
            print("ORDER_LOG table already exists")

        cursor.close()
        conn.close()
    except Exception as e:
        print(f"Error ensuring ORDER_LOG table exists: {e}")

# Call the function to ensure the table exists
ensure_order_log_table_exists()

# API to get order log data
@app.route('/api/order-log')
def get_order_log():
    date_filter = request.args.get('date', '')
    symbol_filter = request.args.get('symbol', '')
    limit = request.args.get('limit', '100')  # Default to 100 rows

    try:
        limit = int(limit)
    except ValueError:
        limit = 100

    # Build the query based on filters
    query = "SELECT * FROM ORDER_LOG"
    params = []
    where_clauses = []

    if date_filter:
        where_clauses.append("DATE(timestamp) = %s")
        params.append(date_filter)

    if symbol_filter:
        where_clauses.append("symbol = %s")
        params.append(symbol_filter)

    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)

    # Add order by to get newest first
    query += " ORDER BY timestamp DESC"

    # Add limit
    query += f" LIMIT {limit}"

    # Execute the query
    try:
        print(f"Executing query: {query} with params: {params}")
        df = get_data_from_db(query, tuple(params))
        print(f"Query returned {len(df)} rows")
        if df.empty:
            print("No data found in ORDER_LOG table")
            return jsonify([])
    except Exception as e:
        print(f"Error executing query: {e}")
        return jsonify({"error": str(e)})

    # Convert to list of dictionaries
    result = df.to_dict('records')

    # Format timestamp for display and handle NaN values
    for item in result:
        # Handle NaN values in all numeric fields
        for key, value in item.items():
            # Check if value is NaN or infinite
            if isinstance(value, float) and (pd.isna(value) or pd.isnull(value) or not np.isfinite(value)):
                item[key] = None

        # Format timestamp
        if 'timestamp' in item and item['timestamp']:
            try:
                # Convert to datetime if it's a string
                if isinstance(item['timestamp'], str):
                    dt = datetime.datetime.strptime(item['timestamp'], '%Y-%m-%d %H:%M:%S')
                else:
                    dt = item['timestamp']

                item['formatted_timestamp'] = dt.strftime('%Y-%m-%d %H:%M:%S')
            except Exception as e:
                item['formatted_timestamp'] = str(item['timestamp'])

    return jsonify(result)

# API to get available dates in order log
@app.route('/api/order-log-dates')
def get_order_log_dates():
    query = "SELECT DISTINCT DATE(timestamp) as date FROM ORDER_LOG ORDER BY date DESC"
    df = get_data_from_db(query)

    if df.empty:
        return jsonify([])

    # Convert dates to strings
    dates = [str(date) for date in df['date']]
    return jsonify(dates)

# API to get available symbols in order log
@app.route('/api/order-log-symbols')
def get_order_log_symbols():
    query = "SELECT DISTINCT symbol FROM ORDER_LOG ORDER BY symbol"
    df = get_data_from_db(query)

    if df.empty:
        return jsonify([])

    symbols = df['symbol'].tolist()
    return jsonify(symbols)

# API to get option chain data for a specific symbol
@app.route('/api/option-chain')
def get_option_chain():
    symbol = request.args.get('symbol', '')

    if not symbol:
        # Get the first available symbol if none specified
        symbols = extract_symbols_from_db()
        if symbols:
            symbol = symbols[0]
        else:
            return jsonify({"error": "No symbols available"})

    # Get option data from option_data_live for this symbol
    query = "SELECT * FROM option_data_live WHERE symbol = ? ORDER BY Strike"
    df_option = get_data_from_db(query, (symbol,))

    # If no data in live table, try getting from historical table
    if df_option.empty:
        # Get the latest timestamp for this symbol from historical table
        query = "SELECT MAX(timestamp) as latest_timestamp FROM option_data WHERE symbol = ?"
        latest_ts_df = get_data_from_db(query, (symbol,))

        if latest_ts_df.empty or pd.isna(latest_ts_df['latest_timestamp'].iloc[0]):
            return jsonify({"error": "No option chain data found for this symbol"})

        latest_timestamp = latest_ts_df['latest_timestamp'].iloc[0]
        # Get all strikes for the latest timestamp from historical table
        query = "SELECT * FROM option_data WHERE symbol = ? AND timestamp = ? ORDER BY Strike"
        df_option = get_data_from_db(query, (symbol, latest_timestamp))

        if df_option.empty:
            return jsonify({"error": "No option chain data found for this symbol"})

    # Convert to list of dictionaries
    option_data = df_option.to_dict('records')

    # Get the latest timestamp
    latest_timestamp = option_data[0]['timestamp'] if option_data else datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    return jsonify({
        "symbol": symbol,
        "option_chain": option_data,
        "timestamp": latest_timestamp
    })

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')

    app.run(debug=True, host='0.0.0.0', port=5000)
