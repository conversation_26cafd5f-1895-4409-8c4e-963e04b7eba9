#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Strategy 2 - Level-Based Algorithmic Trading Bot
-----------------------------------------------
This bot trades options on NIFTY, BANKNIFTY, and FINNIFTY based on trigger levels
that are multiples of 100 (NIFTY/FINNIFTY) or 500 (BANKNIFTY).

Trading Logic:
1. Trigger levels are multiples of 100/500 with buffers
2. Buy CALL (ATM-2) when spot crosses trigger level + buffer from below
3. Buy PUT (ATM+2) when spot crosses trigger level - buffer from above
4. Target is next level in the direction of trade
5. Stop loss is trigger level . buffer
6. Lot sizing based on OI strength analysis
7. Trailing stop loss when near target

Trading Hours: 09:15 AM to 03:25 PM IST
"""

import os
import sys
import time
import datetime
import logging
import pymysql
import configparser
import math
from typing import Dict, Tuple, List, Optional
import requests
import urllib
import json

# Import Zerodha API integration
from zerodhaapi import KiteApp, get_access_token

# Import symbol generation utility
from get_symbols import generate_index_symbol

def send_telegram_message(message):
    """Send message to Telegram"""
    encoded_message = urllib.parse.quote(message)
    base_url = f"https://api.telegram.org/bot6339357671:AAHwZK_7_XhCC_GIb_AwLgXjm6IiVfjqits/sendMessage?chat_id=-1001970206797&text={encoded_message}"
    base_url1 = f"https://api.telegram.org/bot6339357671:AAHwZK_7_XhCC_GIb_AwLgXjm6IiVfjqits/sendMessage?chat_id=-1001878975708&text={encoded_message}"
    response = requests.get(base_url)
    response = requests.get(base_url1)

# Configure logging
logger = logging.getLogger("Strategy2")
logger.setLevel(logging.INFO)
logger.propagate = False

# Create handlers
file_handler = logging.FileHandler("strategy2.log", mode='a')
console_handler = logging.StreamHandler(sys.stdout)

# Create formatters
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Add handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

logger.info("Strategy2 logger initialized")

# Constants
SYMBOLS = ["NIFTY", "BANKNIFTY", "FINNIFTY"]

# Trading hours
MARKET_START_TIME = datetime.time(9, 14)
TRADING_START_TIME = datetime.time(9, 14)
TRADING_END_TIME = datetime.time(15, 25)
SQUARE_OFF_TIME = datetime.time(15, 25)

def get_trigger_multiple(symbol: str) -> int:
    """Get trigger level multiple for symbol"""
    if symbol == "BANKNIFTY":
        return 500
    else:  # NIFTY, FINNIFTY
        return 100

def get_buffer(symbol: str) -> int:
    """Get buffer points for symbol"""
    if symbol == "BANKNIFTY":
        return 25
    else:  # NIFTY, FINNIFTY
        return 10

def get_lot_size(symbol: str) -> int:
    """Get lot size for symbol"""
    if symbol == "NIFTY":
        return 75
    elif symbol == "FINNIFTY":
        return 65
    elif symbol == "BANKNIFTY":
        return 30
    else:
        return 50

def round_to_trigger_level(spot_price: float, symbol: str) -> float:
    """Round spot price to nearest trigger level"""
    multiple = get_trigger_multiple(symbol)
    return round(spot_price / multiple) * multiple

class Strategy2Trader:
    """Level-based algorithmic trading strategy"""

    def __init__(self):
        """Initialize the Strategy2Trader"""
        self.db_config = self._get_db_config()
        self.conn = None
        self.cursor = None
        self._connect_to_db()

        # Initialize Zerodha API
        try:
            self.enctoken = get_access_token()
            self.kite = KiteApp(enctoken=self.enctoken)
            user_profile = self.kite.profile()
            logger.info(f"Connected to Zerodha as {user_profile['user_id']} ({user_profile['user_name']})")

            self.exchange_data = self.kite.instruments()
            logger.info(f"Fetched {len(self.exchange_data)} instruments from Zerodha")
        except Exception as e:
            logger.error(f"Failed to initialize Zerodha API: {e}")
            raise

        # Fetch expiry dates
        self.expiry_dates = self._fetch_expiry_dates()

        # Trading state
        self.positions = {
            symbol: {
                "CE": {
                    "active": False,
                    "strike": None,
                    "entry_price": None,
                    "entry_time": None,
                    "trigger_level": None,
                    "target_level": None,
                    "stop_loss": None,
                    "lot_quantity": 1,
                    "order_id": None,
                    "tradingsymbol": None
                },
                "PE": {
                    "active": False,
                    "strike": None,
                    "entry_price": None,
                    "entry_time": None,
                    "trigger_level": None,
                    "target_level": None,
                    "stop_loss": None,
                    "lot_quantity": 1,
                    "order_id": None,
                    "tradingsymbol": None
                }
            } for symbol in SYMBOLS
        }

        # Market data
        self.market_data = {symbol: {} for symbol in SYMBOLS}
        self.prev_spot_prices = {symbol: None for symbol in SYMBOLS}

        # Check if expiry day
        self.is_expiry_day = self._check_if_expiry_day()

        logger.info(f"Strategy2Trader initialized. Expiry day: {self.is_expiry_day}")

    def _get_db_config(self) -> Dict[str, str]:
        """Get database configuration"""
        config = configparser.ConfigParser()
        if os.path.exists('db_config.ini'):
            config.read('db_config.ini')
            return config['mysql']
        else:
            return {
                'host': 'localhost',
                'port': '3306',
                'user': 'root',
                'password': 'vinayak123',
                'database': 'option_chain_data'
            }

    def _connect_to_db(self) -> None:
        """Connect to MySQL database"""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=int(self.db_config['port']),
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info("Connected to database successfully")

            self._create_order_log_table()

        except Exception as e:
            logger.error(f"Database connection error: {e}")
            sys.exit(1)

    def _create_order_log_table(self) -> None:
        """Create ORDER_LOG table if it doesn't exist"""
        try:
            self.cursor.execute("SHOW TABLES LIKE 'ORDER_LOG'")
            table_exists = self.cursor.fetchone()

            if not table_exists:
                self.cursor.execute("""
                CREATE TABLE ORDER_LOG (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    timestamp DATETIME,
                    symbol VARCHAR(50),
                    order_type VARCHAR(10),
                    option_type VARCHAR(5),
                    strike FLOAT,
                    spot_price FLOAT,
                    option_price FLOAT,
                    reason VARCHAR(255),
                    support FLOAT,
                    resistance FLOAT,
                    pnl FLOAT,
                    status VARCHAR(20)
                )
                """)
                self.conn.commit()
                logger.info("Created ORDER_LOG table")
        except Exception as e:
            logger.error(f"Error creating ORDER_LOG table: {e}")

    def _fetch_expiry_dates(self) -> Dict[str, List[str]]:
        """Fetch expiry dates from database"""
        expiry_dates = {}
        try:
            for symbol in SYMBOLS:
                query = """
                SELECT expiry_date FROM expiries
                WHERE symbol = %s
                ORDER BY expiry_date
                """
                self.cursor.execute(query, (symbol,))
                dates = [row['expiry_date'] for row in self.cursor.fetchall()]

                if dates:
                    expiry_dates[symbol] = dates
                    logger.info(f"Fetched {len(dates)} expiry dates for {symbol}")
                else:
                    logger.warning(f"No expiry dates found for {symbol}")
                    expiry_dates[symbol] = []

            return expiry_dates
        except Exception as e:
            logger.error(f"Error fetching expiry dates: {e}")
            return {symbol: [] for symbol in SYMBOLS}

    def _check_if_expiry_day(self) -> bool:
        """Check if today is expiry day"""
        try:
            today_str = datetime.datetime.now().strftime('%Y-%m-%d')

            if os.path.exists('expiry_dates.txt'):
                with open('expiry_dates.txt', 'r') as f:
                    expiry_dates = [line.strip() for line in f.readlines()]

                is_expiry = today_str in expiry_dates
                logger.info(f"Checking if today ({today_str}) is expiry day: {is_expiry}")
                return is_expiry
            else:
                today_weekday = datetime.datetime.now().weekday()
                is_thursday = today_weekday == 3
                logger.warning("expiry_dates.txt not found. Checking if today is Thursday.")
                return is_thursday
        except Exception as e:
            logger.error(f"Error checking expiry day: {e}")
            today_weekday = datetime.datetime.now().weekday()
            return today_weekday == 3

    def _is_trading_time(self) -> bool:
        """Check if within trading hours"""
        now = datetime.datetime.now().time()
        return TRADING_START_TIME <= now <= TRADING_END_TIME

    def _is_time_to_square_off(self) -> bool:
        """Check if time to square off all positions"""
        now = datetime.datetime.now().time()
        return now >= SQUARE_OFF_TIME

    def _fetch_market_data(self) -> None:
        """Fetch latest market data for all symbols"""
        try:
            for symbol in SYMBOLS:
                query = """
                SELECT * FROM option_data_need_live
                WHERE symbol = %s
                LIMIT 1
                """
                self.cursor.execute(query, (symbol,))
                data = self.cursor.fetchone()

                if data:
                    self.market_data[symbol] = data

                    # Store previous spot price if not set
                    if self.prev_spot_prices.get(symbol) is None:
                        self.prev_spot_prices[symbol] = float(data['Spot_LTP'])

                    logger.debug(f"Fetched market data for {symbol}: Spot price: {data['Spot_LTP']}")
                else:
                    logger.warning(f"No market data found for {symbol}")

            self.conn.commit()
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            self._connect_to_db()

    def _get_atm_oi_data(self, symbol: str, atm_strike: float) -> Tuple[int, int]:
        """Get CE and PE OI change for ATM strike"""
        try:
            query = """
            SELECT CE_OI_Change, PE_OI_Change, CE_OI, PE_OI
            FROM option_data_live
            WHERE symbol = %s AND Strike = %s
            ORDER BY timestamp DESC LIMIT 1
            """
            self.cursor.execute(query, (symbol, atm_strike))
            result = self.cursor.fetchone()

            if result:
                if self.is_expiry_day:
                    # On expiry day, use OI instead of OI change
                    ce_oi_change = int(result['CE_OI'] or 0)
                    pe_oi_change = int(result['PE_OI'] or 0)
                else:
                    # Regular day, use OI change
                    ce_oi_change = int(result['CE_OI_Change'] or 0)
                    pe_oi_change = int(result['PE_OI_Change'] or 0)

                logger.info(f"{symbol} ATM {atm_strike} - CE OI Change: {ce_oi_change}, PE OI Change: {pe_oi_change}")
                return ce_oi_change, pe_oi_change
            else:
                logger.warning(f"No OI data found for {symbol} ATM strike {atm_strike}")
                return 0, 0

        except Exception as e:
            logger.error(f"Error getting ATM OI data for {symbol}: {e}")
            return 0, 0

    def _analyze_oi_strength(self, symbol: str, atm_strike: float) -> Tuple[str, int, int]:
        """
        Analyze OI strength to determine market direction and lot quantities
        Returns: (stronger_side, call_lots, put_lots)
        """
        ce_oi_change, pe_oi_change = self._get_atm_oi_data(symbol, atm_strike)

        # Avoid division by zero
        if ce_oi_change == 0 and pe_oi_change == 0:
            return "NEUTRAL", 1, 1

        # Calculate strength ratios
        if ce_oi_change > 0 and pe_oi_change > 0:
            ce_strength_ratio = pe_oi_change / ce_oi_change if ce_oi_change > 0 else float('inf')
            pe_strength_ratio = ce_oi_change / pe_oi_change if pe_oi_change > 0 else float('inf')

            # Check if difference is less than 75%
            if ce_strength_ratio >= 0.75 and pe_strength_ratio >= 0.75:
                logger.info(f"{symbol} - OI difference less than 75%, no clear direction")
                return "NEUTRAL", 1, 1  # No entry

            # Determine stronger side
            if ce_strength_ratio < 0.75:  # CE is stronger
                logger.info(f"{symbol} - CE OI is stronger (PE is {ce_strength_ratio:.2%} of CE)")
                return "CE_STRONG", 1, 2  # 1 lot for CE, 2 lots for PE
            elif pe_strength_ratio < 0.75:  # PE is stronger
                logger.info(f"{symbol} - PE OI is stronger (CE is {pe_strength_ratio:.2%} of PE)")
                return "PE_STRONG", 2, 1  # 2 lots for CE, 1 lot for PE

        return "NEUTRAL", 1, 1

    def _get_option_strikes(self, symbol: str) -> Tuple[float, float, float]:
        """
        Get ATM, CE (ATM-2), and PE (ATM+2) strikes
        Returns: (atm_strike, ce_strike, pe_strike)
        """
        try:
            spot_price = float(self.market_data[symbol]['Spot_LTP'])

            # Get available strikes
            query = """
            SELECT DISTINCT Strike FROM option_data_live
            WHERE symbol = %s
            ORDER BY Strike
            """
            self.cursor.execute(query, (symbol,))
            strikes = [float(row['Strike']) for row in self.cursor.fetchall()]

            if not strikes:
                logger.warning(f"No strikes found for {symbol}")
                return None, None, None

            # Find ATM strike
            atm_strike = min(strikes, key=lambda x: abs(x - spot_price))
            atm_index = strikes.index(atm_strike)

            # Get CE strike (ATM-2)
            ce_strike = None
            if atm_index >= 2:
                ce_strike = strikes[atm_index - 2]
            elif atm_index >= 1:
                ce_strike = strikes[atm_index - 1]
            else:
                ce_strike = atm_strike

            # Get PE strike (ATM+2)
            pe_strike = None
            if atm_index + 2 < len(strikes):
                pe_strike = strikes[atm_index + 2]
            elif atm_index + 1 < len(strikes):
                pe_strike = strikes[atm_index + 1]
            else:
                pe_strike = atm_strike

            logger.info(f"{symbol} - Spot: {spot_price}, ATM: {atm_strike}, CE (ATM-2): {ce_strike}, PE (ATM+2): {pe_strike}")
            return atm_strike, ce_strike, pe_strike

        except Exception as e:
            logger.error(f"Error getting option strikes for {symbol}: {e}")
            return None, None, None

    def _check_trigger_crossover(self, symbol: str) -> Tuple[bool, bool, float]:
        """
        Check if spot price crossed trigger levels
        Returns: (call_trigger, put_trigger, trigger_level)
        """
        try:
            current_spot = float(self.market_data[symbol]['Spot_LTP'])
            prev_spot = self.prev_spot_prices[symbol]

            if prev_spot is None:
                return False, False, None

            buffer = get_buffer(symbol)
            multiple = get_trigger_multiple(symbol)

            # Find the trigger level that was crossed
            prev_trigger_level = round_to_trigger_level(prev_spot, symbol)
            current_trigger_level = round_to_trigger_level(current_spot, symbol)

            call_trigger = False
            put_trigger = False
            trigger_level = None

            # Check for upward crossover (CALL trigger)
            if current_spot > prev_spot:
                # Check if we crossed above trigger level + buffer
                for level in range(int(prev_trigger_level), int(current_trigger_level) + multiple, multiple):
                    trigger_with_buffer = level + buffer
                    if prev_spot < trigger_with_buffer <= current_spot:
                        call_trigger = True
                        trigger_level = level
                        logger.info(f"{symbol} - CALL trigger: crossed {trigger_with_buffer} from below")
                        break

            # Check for downward crossover (PUT trigger)
            elif current_spot < prev_spot:
                # Check if we crossed below trigger level - buffer
                for level in range(int(prev_trigger_level), int(current_trigger_level) - multiple, -multiple):
                    trigger_with_buffer = level - buffer
                    if prev_spot > trigger_with_buffer >= current_spot:
                        put_trigger = True
                        trigger_level = level
                        logger.info(f"{symbol} - PUT trigger: crossed {trigger_with_buffer} from above")
                        break

            return call_trigger, put_trigger, trigger_level

        except Exception as e:
            logger.error(f"Error checking trigger crossover for {symbol}: {e}")
            return False, False, None

    def _get_closest_expiry(self, symbol: str) -> Optional[str]:
        """Get closest expiry date for symbol"""
        today = datetime.datetime.now().date()

        if symbol not in self.expiry_dates or not self.expiry_dates[symbol]:
            logger.warning(f"No expiry dates available for {symbol}")
            return None

        # Filter future expiry dates
        valid_expiries = [
            expiry for expiry in self.expiry_dates[symbol]
            if datetime.datetime.strptime(expiry, '%Y-%m-%d').date() >= today
        ]

        if not valid_expiries:
            logger.warning(f"No future expiry dates available for {symbol}")
            return None

        return valid_expiries[0]

    def _generate_trading_symbol(self, symbol: str, strike: float, option_type: str) -> Optional[str]:
        """Generate Zerodha trading symbol"""
        try:
            expiry_date = self._get_closest_expiry(symbol)
            if not expiry_date:
                logger.error(f"Could not find valid expiry date for {symbol}")
                return None

            trading_symbol = generate_index_symbol(
                index_name=symbol,
                expiry_date=expiry_date,
                strike_price=strike,
                option_type=option_type
            )

            logger.info(f"Generated trading symbol: {trading_symbol}")
            return trading_symbol
        except Exception as e:
            logger.error(f"Error generating trading symbol: {e}")
            return None

    def _buy_option(self, symbol: str, option_type: str, trigger_level: float, lot_quantity: int) -> None:
        """Buy option (CE or PE)"""
        if self.positions[symbol][option_type]["active"]:
            logger.info(f"{symbol} - {option_type} position already active")
            return

        # Get strikes
        atm_strike, ce_strike, pe_strike = self._get_option_strikes(symbol)
        if not atm_strike:
            logger.warning(f"{symbol} - Could not determine strikes")
            return

        strike = ce_strike if option_type == "CE" else pe_strike
        if not strike:
            logger.warning(f"{symbol} - Could not determine {option_type} strike")
            return

        # Get current market data
        current_spot = float(self.market_data[symbol]['Spot_LTP'])
        buffer = get_buffer(symbol)
        multiple = get_trigger_multiple(symbol)

        # Calculate target and stop loss
        if option_type == "CE":
            target_level = trigger_level + multiple - buffer
            stop_loss = trigger_level - buffer
        else:  # PE
            target_level = trigger_level - multiple + buffer
            stop_loss = trigger_level + buffer

        # Generate trading symbol
        trading_symbol = self._generate_trading_symbol(symbol, strike, option_type)
        if not trading_symbol:
            logger.error(f"{symbol} - Could not generate trading symbol")
            return

        # Get option price
        try:
            query = """
            SELECT CE_LTP, PE_LTP FROM option_data_live
            WHERE symbol = %s AND Strike = %s
            ORDER BY timestamp DESC LIMIT 1
            """
            self.cursor.execute(query, (symbol, strike))
            result = self.cursor.fetchone()

            if result:
                option_price = float(result[f'{option_type}_LTP'] or 0)
            else:
                logger.warning(f"{symbol} - No option price found for {strike} {option_type}")
                option_price = 0
        except Exception as e:
            logger.error(f"Error getting option price: {e}")
            option_price = 0

        # Calculate total quantity
        base_lot_size = get_lot_size(symbol)
        total_quantity = base_lot_size * lot_quantity

        # Place order with Zerodha
        try:
            # order_response = self.kite.place_order(
            #     variety=self.kite.VARIETY_REGULAR,
            #     exchange=self.kite.EXCHANGE_NFO,
            #     tradingsymbol=trading_symbol,
            #     transaction_type=self.kite.TRANSACTION_TYPE_BUY,
            #     quantity=total_quantity,
            #     product=self.kite.PRODUCT_MIS,  # Intraday
            #     order_type=self.kite.ORDER_TYPE_MARKET,
            # )

            # order_id = order_response.get("data", {}).get("order_id")
            # if not order_id:
            #     logger.error(f"{symbol} - Failed to get order ID: {order_response}")
            #     return

            # Update position tracking
            self.positions[symbol][option_type] = {
                "active": True,
                "strike": strike,
                "entry_price": option_price,
                "entry_time": datetime.datetime.now(),
                "trigger_level": trigger_level,
                "target_level": target_level,
                "stop_loss": stop_loss,
                "lot_quantity": lot_quantity,
                # "order_id": order_id,
                "tradingsymbol": trading_symbol
            }

            # Log to database
            reason = f"Trigger level {trigger_level} crossed, {lot_quantity} lots"
            self._log_order(symbol, "BUY", option_type, strike, current_spot, option_price, reason, 0, 0, None, "EXECUTED")

            # Send Telegram message
            message = f"🟢 BUY {symbol} {option_type} {strike} @ {option_price} | Qty: {total_quantity} | Trigger: {trigger_level} | Target: {target_level} | SL: {stop_loss}"
            send_telegram_message(message)

            logger.info(f"{symbol} - Bought {option_type} {strike} @ {option_price}, Qty: {total_quantity}")

        except Exception as e:
            logger.error(f"{symbol} - Error placing buy order: {e}")
            self._log_order(symbol, "BUY", option_type, strike, current_spot, option_price, f"Order failed: {e}", 0, 0, None, "FAILED")

    def _sell_option(self, symbol: str, option_type: str, reason: str) -> None:
        """Sell option (CE or PE)"""
        position = self.positions[symbol][option_type]
        if not position["active"]:
            logger.info(f"{symbol} - No active {option_type} position to sell")
            return

        # Get current option price
        try:
            query = """
            SELECT CE_LTP, PE_LTP FROM option_data_live
            WHERE symbol = %s AND Strike = %s
            ORDER BY timestamp DESC LIMIT 1
            """
            self.cursor.execute(query, (symbol, position["strike"]))
            result = self.cursor.fetchone()

            if result:
                current_price = float(result[f'{option_type}_LTP'] or 0)
            else:
                logger.warning(f"{symbol} - No current price found for {position['strike']} {option_type}")
                current_price = position["entry_price"]
        except Exception as e:
            logger.error(f"Error getting current option price: {e}")
            current_price = position["entry_price"]

        # Calculate P&L
        base_lot_size = get_lot_size(symbol)
        total_quantity = base_lot_size * position["lot_quantity"]
        pnl = (current_price - position["entry_price"]) * total_quantity

        # Place sell order
        try:
            # order_response = self.kite.place_order(
            #     variety=self.kite.VARIETY_REGULAR,
            #     exchange=self.kite.EXCHANGE_NFO,
            #     tradingsymbol=position["tradingsymbol"],
            #     transaction_type=self.kite.TRANSACTION_TYPE_SELL,
            #     quantity=total_quantity,
            #     product=self.kite.PRODUCT_MIS,
            #     order_type=self.kite.ORDER_TYPE_MARKET,
            # )

            # order_id = order_response.get("data", {}).get("order_id")
            # if not order_id:
            #     logger.error(f"{symbol} - Failed to get sell order ID: {order_response}")
            #     return

            # Update position tracking
            self.positions[symbol][option_type] = {
                "active": False,
                "strike": None,
                "entry_price": None,
                "entry_time": None,
                "trigger_level": None,
                "target_level": None,
                "stop_loss": None,
                "lot_quantity": 1,
                "order_id": None,
                "tradingsymbol": None
            }

            # Log to database
            current_spot = float(self.market_data[symbol]['Spot_LTP'])
            self._log_order(symbol, "SELL", option_type, position["strike"], current_spot, current_price, reason, 0, 0, pnl, "EXECUTED")

            # Send Telegram message
            pnl_emoji = "🟢" if pnl >= 0 else "🔴"
            message = f"{pnl_emoji} SELL {symbol} {option_type} {position['strike']} @ {current_price} | P&L: ₹{pnl:.2f} | Reason: {reason}"
            send_telegram_message(message)

            logger.info(f"{symbol} - Sold {option_type} {position['strike']} @ {current_price}, P&L: ₹{pnl:.2f}")

        except Exception as e:
            logger.error(f"{symbol} - Error placing sell order: {e}")

    def _log_order(self, symbol: str, order_type: str, option_type: str, strike: float,
                   spot_price: float, option_price: float, reason: str, support: float,
                   resistance: float, pnl: Optional[float], status: str) -> None:
        """Log order to database"""
        try:
            query = """
            INSERT INTO ORDER_LOG (timestamp, symbol, order_type, option_type, strike,
                                 spot_price, option_price, reason, support, resistance, pnl, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            self.cursor.execute(query, (
                datetime.datetime.now(), symbol, order_type, option_type, strike,
                spot_price, option_price, reason, support, resistance, pnl, status
            ))
            self.conn.commit()
        except Exception as e:
            logger.error(f"Error logging order: {e}")

    def _check_exit_conditions(self, symbol: str) -> None:
        """Check exit conditions for active positions"""
        current_spot = float(self.market_data[symbol]['Spot_LTP'])
        prev_spot = self.prev_spot_prices[symbol]

        if prev_spot is None:
            return

        # Check CE position
        ce_position = self.positions[symbol]["CE"]
        if ce_position["active"]:
            # Check target hit
            if current_spot >= ce_position["target_level"]:
                self._sell_option(symbol, "CE", f"Target hit at {ce_position['target_level']}")
            # Check stop loss
            elif current_spot <= ce_position["stop_loss"]:
                self._sell_option(symbol, "CE", f"Stop loss hit at {ce_position['stop_loss']}")
            # Check trailing stop loss
            elif self._check_trailing_stop_loss(symbol, "CE", current_spot, prev_spot):
                self._sell_option(symbol, "CE", "Trailing stop loss triggered")

        # Check PE position
        pe_position = self.positions[symbol]["PE"]
        if pe_position["active"]:
            # Check target hit
            if current_spot <= pe_position["target_level"]:
                self._sell_option(symbol, "PE", f"Target hit at {pe_position['target_level']}")
            # Check stop loss
            elif current_spot >= pe_position["stop_loss"]:
                self._sell_option(symbol, "PE", f"Stop loss hit at {pe_position['stop_loss']}")
            # Check trailing stop loss
            elif self._check_trailing_stop_loss(symbol, "PE", current_spot, prev_spot):
                self._sell_option(symbol, "PE", "Trailing stop loss triggered")

    def _check_trailing_stop_loss(self, symbol: str, option_type: str, current_spot: float, prev_spot: float) -> bool:
        """Check trailing stop loss condition"""
        position = self.positions[symbol][option_type]
        if not position["active"]:
            return False

        target_level = position["target_level"]

        if option_type == "CE":
            # For CE: if spot comes within 10 points of target but then moves away
            near_target = abs(current_spot - target_level) <= 10
            moving_away = current_spot < prev_spot and prev_spot >= (target_level - 10)
            return near_target and moving_away
        else:  # PE
            # For PE: if spot comes within 10 points of target but then moves away
            near_target = abs(current_spot - target_level) <= 10
            moving_away = current_spot > prev_spot and prev_spot <= (target_level + 10)
            return near_target and moving_away

    def _square_off_all_positions(self) -> None:
        """Square off all active positions at market close"""
        logger.info("Squaring off all positions at market close")

        for symbol in SYMBOLS:
            if self.positions[symbol]["CE"]["active"]:
                self._sell_option(symbol, "CE", "Market close square off")
            if self.positions[symbol]["PE"]["active"]:
                self._sell_option(symbol, "PE", "Market close square off")

    def run(self) -> None:
        """Main trading loop"""
        logger.info("Starting Strategy2 trading bot")

        try:
            while True:
                current_time = datetime.datetime.now().time()

                # Check if market is closed
                if current_time < TRADING_START_TIME or current_time > TRADING_END_TIME:
                    if TRADING_START_TIME <= current_time <= TRADING_END_TIME:
                        logger.info("Market closed, stopping bot")
                        break
                    time.sleep(60)  # Wait 1 minute before checking again
                    continue

                # Square off at closing time
                if self._is_time_to_square_off():
                    self._square_off_all_positions()
                    break

                # Fetch market data
                self._fetch_market_data()

                # Process each symbol
                for symbol in SYMBOLS:
                    if symbol not in self.market_data:
                        continue

                    # Check exit conditions first
                    self._check_exit_conditions(symbol)

                    # Check for new entry signals
                    call_trigger, put_trigger, trigger_level = self._check_trigger_crossover(symbol)

                    if call_trigger or put_trigger:
                        # Get ATM strike for OI analysis
                        atm_strike, _, _ = self._get_option_strikes(symbol)
                        if atm_strike:
                            # Analyze OI strength
                            oi_strength, call_lots, put_lots = self._analyze_oi_strength(symbol, atm_strike)

                            # Execute trades based on signals and OI strength
                            if call_trigger and call_lots > 0:
                                self._buy_option(symbol, "CE", trigger_level, call_lots)

                            if put_trigger and put_lots > 0:
                                self._buy_option(symbol, "PE", trigger_level, put_lots)

                    # Update previous spot price
                    self.prev_spot_prices[symbol] = float(self.market_data[symbol]['Spot_LTP'])

                # Wait before next iteration
                time.sleep(1)  # 1 second interval

        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        except Exception as e:
            logger.error(f"Error in main loop: {e}")
        finally:
            # Square off any remaining positions
            self._square_off_all_positions()
            logger.info("Strategy2 trading bot stopped")

if __name__ == "__main__":
    trader = Strategy2Trader()
    trader.run()
