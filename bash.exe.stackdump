Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB6A0, 0007FFFFA5A0) msys-2.0.dll+0x2118E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 0007FFFFB6A0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6A0  00021006A545 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF814350000 ntdll.dll
7FF812100000 KERNEL32.DLL
7FF811840000 KERNELBASE.dll
7FF812E00000 USER32.dll
7FF812050000 win32u.dll
7FF814260000 GDI32.dll
7FF811660000 gdi32full.dll
7FF811FB0000 msvcp_win.dll
7FF811E60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8141A0000 advapi32.dll
7FF813790000 msvcrt.dll
7FF812380000 sechost.dll
7FF811F80000 bcrypt.dll
7FF813570000 RPCRT4.dll
7FF810E00000 CRYPTBASE.DLL
7FF811C70000 bcryptPrimitives.dll
7FF8136F0000 IMM32.DLL
