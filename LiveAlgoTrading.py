#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NSE FNO Live Algorithmic Trading Bot with Zerodha Integration
------------------------------------------------------------
This bot trades options on NIFTY, BANKNIFTY, FINNIFTY, and MIDCPNIFTY based on
support and resistance levels derived from option chain data.

This is a live trading version that integrates with Zerodha broker API to execute real trades.
It uses the same trading logic as the simulation version but places actual orders.

The bot follows these rules:
1. Only trades between 9:20 AM and 3:15 PM
2. Only takes trades if difference between resistance and support is more than 51 points
3. Trades only ITM (ATM-1) strikes
4. Enters or exits positions for all indices if conditions are met in any one of them
5. Uses MAX_CALL_OI and MAX_CALL_CHANGE_IN_OI as resistance points
   - Sells CE positions when spot price crosses resistance from below to above
6. Uses MAX_PUT_OI and MAX_PUT_CHANGE_IN_OI as support points
   - Buys CE positions when spot price crosses support from below to above
7. Buys PE positions when spot price crosses resistance from above to below
8. Sells PE positions when spot price crosses support from above to below
9. Implements buffer zones of 10 points for resistance and support
10. Implements stoploss of 10 points below support for CE and 10 points above resistance for PE
11. Prioritizes MAX_CALL_CHANGE_IN_OI as resistance over MAX_CALL_OI on normal days
12. Prioritizes MAX_PUT_CHANGE_IN_OI as support over MAX_PUT_OI on normal days
13. On expiry days, prioritizes MAX_CALL_OI as resistance and MAX_PUT_OI as support
14. Calculates P&L using option prices
15. Logs all orders with detailed reasons in the ORDER_LOG table
16. Syncs positions with Zerodha broker
17. Uses actual expiry dates from MySQL database
"""

import os
import sys
import time
import datetime
import logging
import pymysql
import configparser
import math
from typing import Dict, Tuple, List, Optional
import requests
import urllib
import json

# Import Zerodha API integration
from zerodhaapi import KiteApp, get_access_token

# Import symbol generation utility
from get_symbols import generate_index_symbol

def send_telegram_message(message):
    encoded_message = urllib.parse.quote(message)
    base_url = f"https://api.telegram.org/bot6339357671:AAHwZK_7_XhCC_GIb_AwLgXjm6IiVfjqits/sendMessage?chat_id=-1001970206797&text={encoded_message}"
    base_url1 = f"https://api.telegram.org/bot6339357671:AAHwZK_7_XhCC_GIb_AwLgXjm6IiVfjqits/sendMessage?chat_id=-1001878975708&text={encoded_message}"
    response = requests.get(base_url)
    response = requests.get(base_url1)


# Configure logging
# Reset root logger to avoid conflicts with other scripts
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

# Create a specific logger for LiveAlgoTrading
logger = logging.getLogger("LiveAlgoTrader")
logger.setLevel(logging.INFO)
logger.propagate = False  # Prevent propagation to root logger

# Create handlers
file_handler = logging.FileHandler("live_algo_trading.log", mode='a')
console_handler = logging.StreamHandler(sys.stdout)

# Create formatters and add it to handlers
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Add handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Force a test log message to verify logging is working
logger.info("LiveAlgoTrading logger initialized")

# Write a test message to the log file directly to verify file permissions
try:
    with open("live_algo_trading.log", "a") as f:
        f.write(f"{datetime.datetime.now()} - Direct file write test\n")
except Exception as e:
    print(f"Error writing directly to log file: {e}")

# Constants
ALL_SYMBOLS = ["NIFTY", "BANKNIFTY", "FINNIFTY"]
# SYMBOLS will be dynamically updated based on OI conditions
SYMBOLS = ALL_SYMBOLS.copy()

def get_min_range(symbol: str) -> int:
    """
    Get the appropriate minimum range between support and resistance based on the symbol.

    BANKNIFTY requires a minimum range of 201 points.
    All other symbols require a minimum range of 51 points.
    """
    if symbol == "BANKNIFTY":
        return 201  # Minimum range for BANKNIFTY
    else:
        return 51   # Minimum range for all other symbols

def get_lot_size(symbol: str) -> int:
    """
    Get the lot size for a specific symbol.

    NIFTY: 75 lots
    FINNIFTY: 65 lots
    BANKNIFTY: 30 lots
    MIDCPNIFTY: 120 lots
    """
    if symbol == "NIFTY":
        return 75
    elif symbol == "FINNIFTY":
        return 65
    elif symbol == "BANKNIFTY":
        return 30
    elif symbol == "MIDCPNIFTY":
        return 120
    else:
        # Default lot size if symbol is not recognized
        logger.warning(f"Unknown symbol for lot size: {symbol}. Using default lot size of 50.")
        return 50

def get_buffer_and_stoploss(symbol: str) -> tuple:
    """
    Get the appropriate buffer and stoploss level based on the symbol.
    Returns a tuple of (buffer, stoploss_level).

    BANKNIFTY has a buffer and stoploss of 25 points.
    All other symbols have a buffer and stoploss of 10 points.
    """
    if symbol == "BANKNIFTY":
        return 25, 25  # Buffer and stoploss for BANKNIFTY
    else:
        return 10, 10  # Buffer and stoploss for all other symbols

def get_opposite_buffer(symbol: str) -> int:
    """
    Get the opposite buffer for a symbol.
    This buffer is used to check if other symbols are near their opposite levels.

    NIFTY: 30 points
    FINNIFTY: 30 points
    BANKNIFTY: 70 points
    MIDCPNIFTY: 30 points (assumed same as others)
    """
    if symbol == "BANKNIFTY":
        return 101
    else:
        return 51

def round_to_nearest_100(value: float) -> float:
    """
    Round a value to the nearest multiple of 100.

    Args:
        value: The value to round

    Returns:
        The value rounded to the nearest multiple of 100
    """
    return round(value / 100) * 100

# Trading hours
MARKET_START_TIME = datetime.time(9, 15)  # Market opens at 9:15 AM
TRADING_START_TIME = datetime.time(9, 20)  # We start trading at 9:20 AM
TRADING_END_TIME = datetime.time(15, 15)  # We end trading at 3:15 PM
MARKET_END_TIME = datetime.time(15, 30)  # Market closes at 3:30 PM

class LiveAlgoTrader:
    """
    Live Algorithmic Trading Bot for NSE FNO options with Zerodha integration.
    """

    def __init__(self):
        """Initialize the LiveAlgoTrader with database connection, Zerodha API, and state variables."""
        self.db_config = self._get_db_config()
        self.conn = None
        self.cursor = None
        self._connect_to_db()

        # Initialize Zerodha API
        try:
            self.enctoken = get_access_token()
            self.kite = KiteApp(enctoken=self.enctoken)
            user_profile = self.kite.profile()
            logger.info(f"Connected to Zerodha as {user_profile['user_id']} ({user_profile['user_name']})")

            # Get exchange data for symbol generation
            self.exchange_data = self.kite.instruments()
            logger.info(f"Fetched {len(self.exchange_data)} instruments from Zerodha")
        except Exception as e:
            logger.error(f"Failed to initialize Zerodha API: {e}")
            raise

        # Fetch expiry dates for each symbol from MySQL
        self.expiry_dates = self._fetch_expiry_dates()

        # Trading state - initialize for ALL_SYMBOLS to handle dynamic addition/removal
        self.positions = {
            symbol: {
                "CE": {
                    "active": False,
                    "strike": None,
                    "entry_price": None,
                    "entry_time": None,
                    "entry_support": None,  # Store support at entry time
                    "entry_resistance": None,  # Store resistance at entry time
                    "trigger_symbol": None,  # Store which symbol triggered this position
                    "is_outbound_trade": False,  # Flag to indicate if this is an outbound trade
                    "order_id": None,  # Zerodha order ID
                    "tradingsymbol": None  # Zerodha trading symbol
                },
                "PE": {
                    "active": False,
                    "strike": None,
                    "entry_price": None,
                    "entry_time": None,
                    "entry_support": None,  # Store support at entry time
                    "entry_resistance": None,  # Store resistance at entry time
                    "trigger_symbol": None,  # Store which symbol triggered this position
                    "is_outbound_trade": False,  # Flag to indicate if this is an outbound trade
                    "order_id": None,  # Zerodha order ID
                    "tradingsymbol": None  # Zerodha trading symbol
                }
            } for symbol in ALL_SYMBOLS  # Use ALL_SYMBOLS to ensure we have entries for all possible symbols
        }

        # Market data - initialize for ALL_SYMBOLS
        self.market_data = {symbol: {} for symbol in ALL_SYMBOLS}

        # Previous values for tracking crossovers - initialize for ALL_SYMBOLS
        self.prev_spot_prices = {symbol: None for symbol in ALL_SYMBOLS}

        # Outbound trade tracking
        self.outbound_mode = False
        self.outbound_direction = None  # "SUPPORT" or "RESISTANCE"
        self.outbound_trigger_time = None
        self.outbound_trigger_symbol = None
        self.stored_spot_prices = {symbol: None for symbol in ALL_SYMBOLS}
        self.outbound_trade_support = {symbol: None for symbol in ALL_SYMBOLS}
        self.outbound_trade_resistance = {symbol: None for symbol in ALL_SYMBOLS}
        self.prev_outbound_spot_prices = {symbol: None for symbol in ALL_SYMBOLS}

        # Store historical spot prices for outbound entry checks (3-4 seconds back)
        self.outbound_spot_history = {symbol: [] for symbol in ALL_SYMBOLS}
        self.last_outbound_history_update = datetime.datetime.now()

        # Flag to check if it's an expiry day
        self.is_expiry_day = self._check_if_expiry_day()

        # Sync positions with Zerodha
        self._sync_positions_with_zerodha()

        logger.info(f"LiveAlgoTrader initialized. Expiry day: {self.is_expiry_day}")

    def _fetch_expiry_dates(self) -> Dict[str, List[str]]:
        """
        Fetch expiry dates for each symbol from the MySQL database.
        Returns a dictionary with symbol as key and list of expiry dates as value.
        """
        expiry_dates = {}
        try:
            for symbol in ALL_SYMBOLS:
                query = """
                SELECT expiry_date FROM expiries
                WHERE symbol = %s
                ORDER BY expiry_date
                """
                self.cursor.execute(query, (symbol,))
                dates = [row['expiry_date'] for row in self.cursor.fetchall()]

                if dates:
                    expiry_dates[symbol] = dates
                    logger.info(f"Fetched {len(dates)} expiry dates for {symbol}")
                else:
                    logger.warning(f"No expiry dates found for {symbol} in database")
                    expiry_dates[symbol] = []

            return expiry_dates
        except Exception as e:
            logger.error(f"Error fetching expiry dates: {e}")
            return {symbol: [] for symbol in ALL_SYMBOLS}

    def _get_closest_expiry(self, symbol: str) -> Optional[str]:
        """
        Get the closest expiry date for a symbol that is after today.
        Returns the expiry date in YYYY-MM-DD format or None if no valid expiry is found.
        """
        today = datetime.datetime.now().date()

        if symbol not in self.expiry_dates or not self.expiry_dates[symbol]:
            logger.warning(f"No expiry dates available for {symbol}")
            return None

        # Filter expiry dates that are after today
        valid_expiries = [
            expiry for expiry in self.expiry_dates[symbol]
            if datetime.datetime.strptime(expiry, '%Y-%m-%d').date() >= today
        ]

        if not valid_expiries:
            logger.warning(f"No future expiry dates available for {symbol}")
            return None

        # Return the closest expiry date
        return valid_expiries[0]

    def _generate_trading_symbol(self, symbol: str, strike: float, option_type: str) -> Optional[str]:
        """
        Generate a valid Zerodha trading symbol for the given parameters.
        """
        try:
            # Get the closest expiry date
            expiry_date = self._get_closest_expiry(symbol)
            if not expiry_date:
                logger.error(f"Could not find valid expiry date for {symbol}")
                return None

            # Generate the trading symbol
            trading_symbol = generate_index_symbol(
                index_name=symbol,
                expiry_date=expiry_date,
                strike_price=strike,
                option_type=option_type
            )

            logger.info(f"Generated trading symbol: {trading_symbol} for {symbol} {strike} {option_type}")
            return trading_symbol
        except Exception as e:
            logger.error(f"Error generating trading symbol: {e}")
            return None

    def _sync_positions_with_zerodha(self) -> None:
        """
        Sync local position tracking with actual Zerodha positions.
        """
        try:
            # Get current positions from Zerodha
            zerodha_positions = self.kite.positions()

            if not zerodha_positions:
                logger.info("No active positions found in Zerodha")
                return

            # Process day positions (we're only interested in intraday positions)
            day_positions = zerodha_positions.get('day', [])

            if not day_positions:
                logger.info("No day positions found in Zerodha")
                return

            logger.info(f"Found {len(day_positions)} day positions in Zerodha")

            # Map Zerodha positions to our tracking structure
            for position in day_positions:
                tradingsymbol = position['tradingsymbol']

                # Skip if not an options position or not one of our symbols
                if not any(symbol in tradingsymbol for symbol in ALL_SYMBOLS):
                    continue

                # Determine symbol, option type, and strike
                for symbol in ALL_SYMBOLS:
                    if symbol in tradingsymbol:
                        option_type = "CE" if "CE" in tradingsymbol else "PE" if "PE" in tradingsymbol else None

                        if not option_type:
                            continue

                        # Extract strike price from trading symbol
                        strike = None
                        for part in tradingsymbol.replace(symbol, "").replace(option_type, "").split():
                            try:
                                strike = float(part)
                                break
                            except ValueError:
                                continue

                        if not strike:
                            continue

                        # Update our position tracking
                        if position['quantity'] > 0:  # Long position
                            self.positions[symbol][option_type] = {
                                "active": True,
                                "strike": strike,
                                "entry_price": position['average_price'],
                                "entry_time": datetime.datetime.now(),  # Approximate
                                "entry_support": None,  # We don't know this
                                "entry_resistance": None,  # We don't know this
                                "trigger_symbol": symbol,  # Assume self-triggered
                                "is_outbound_trade": False,  # Assume normal trade
                                "order_id": None,  # We don't have this
                                "tradingsymbol": tradingsymbol
                            }
                            logger.info(f"Synced {symbol} {option_type} position at strike {strike} from Zerodha")

            logger.info("Position sync with Zerodha completed")
        except Exception as e:
            logger.error(f"Error syncing positions with Zerodha: {e}")
            # Continue with local tracking

    def _get_db_config(self) -> Dict[str, str]:
        """Get database configuration from config file or use defaults."""
        config = configparser.ConfigParser()
        if os.path.exists('db_config.ini'):
            config.read('db_config.ini')
            return config['mysql']
        else:
            # Default configuration
            return {
                'host': 'localhost',
                'port': '3306',
                'user': 'root',
                'password': 'vinayak123',
                'database': 'option_chain_data'
            }

    def _connect_to_db(self) -> None:
        """Connect to the MySQL database and ensure required tables exist."""
        try:
            self.conn = pymysql.connect(
                host=self.db_config['host'],
                port=int(self.db_config['port']),
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database']
            )
            self.cursor = self.conn.cursor(pymysql.cursors.DictCursor)
            logger.info("Connected to database successfully")

            # Create ORDER_LOG table if it doesn't exist
            self._create_order_log_table()

        except Exception as e:
            logger.error(f"Database connection error: {e}")
            sys.exit(1)

    def _create_order_log_table(self) -> None:
        """Create ORDER_LOG table if it doesn't exist."""
        try:
            self.cursor.execute("SHOW TABLES LIKE 'ORDER_LOG'")
            table_exists = self.cursor.fetchone()

            if not table_exists:
                self.cursor.execute("""
                CREATE TABLE ORDER_LOG (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    timestamp DATETIME,
                    symbol VARCHAR(50),
                    order_type VARCHAR(10),  -- BUY or SELL
                    option_type VARCHAR(5),   -- CE or PE
                    strike FLOAT,
                    spot_price FLOAT,
                    option_price FLOAT,
                    reason VARCHAR(255),      -- Reason for the trade
                    support FLOAT,            -- Support level at time of trade
                    resistance FLOAT,         -- Resistance level at time of trade
                    pnl FLOAT,               -- P&L for the trade (NULL for BUY orders)
                    status VARCHAR(20)        -- EXECUTED, FAILED, etc.
                )
                """)
                self.conn.commit()
                logger.info("Created ORDER_LOG table")
        except Exception as e:
            logger.error(f"Error creating ORDER_LOG table: {e}")

    def _check_if_expiry_day(self) -> bool:
        """
        Check if today is an expiry day by reading from expiry_dates.txt file.
        Falls back to checking if today is Thursday if the file doesn't exist or has issues.
        """
        try:
            # Get today's date in YYYY-MM-DD format
            today_str = datetime.datetime.now().strftime('%Y-%m-%d')

            # Check if expiry_dates.txt exists
            if os.path.exists('expiry_dates.txt'):
                with open('expiry_dates.txt', 'r') as f:
                    expiry_dates = [line.strip() for line in f.readlines()]

                # Check if today's date is in the list of expiry dates
                is_expiry = today_str in expiry_dates
                logger.info(f"Checking if today ({today_str}) is an expiry day based on expiry_dates.txt: {is_expiry}")
                return is_expiry
            else:
                # Fallback to checking if today is Thursday
                today_weekday = datetime.datetime.now().weekday()
                is_thursday = today_weekday == 3
                logger.warning("expiry_dates.txt not found. Falling back to checking if today is Thursday.")
                return is_thursday
        except Exception as e:
            # If there's any error, fallback to checking if today is Thursday
            logger.error(f"Error checking expiry day from file: {e}. Falling back to checking if today is Thursday.")
            today_weekday = datetime.datetime.now().weekday()
            return today_weekday == 3

    def _is_market_open(self) -> bool:
        """Check if the market is currently open."""
        now = datetime.datetime.now().time()
        return MARKET_START_TIME <= now <= MARKET_END_TIME

    def _is_trading_time(self) -> bool:
        """Check if it's within our trading hours."""
        now = datetime.datetime.now().time()
        return TRADING_START_TIME <= now <= TRADING_END_TIME

    def _is_time_to_square_off(self) -> bool:
        """Check if it's time to square off all positions (3:15 PM)."""
        now = datetime.datetime.now().time()
        return now >= TRADING_END_TIME

    def _check_finnifty_oi(self) -> bool:
        """
        Check if FINNIFTY has sufficient OI (>= 250K) for either max_call_oi or max_put_oi.
        Returns True if FINNIFTY has sufficient OI, False otherwise.
        """
        try:
            if "FINNIFTY" not in self.market_data:
                return False

            data = self.market_data["FINNIFTY"]
            max_call_oi = float(data.get('Max_Call_OI', 0))
            max_put_oi = float(data.get('Max_Put_OI', 0))

            # Check if either max_call_oi or max_put_oi is >= 4000
            has_sufficient_oi = max_call_oi >= 4000 or max_put_oi >= 4000

            if has_sufficient_oi:
                logger.info(f"FINNIFTY has sufficient OI: Max Call OI = {max_call_oi}, Max Put OI = {max_put_oi}")
            else:
                logger.info(f"FINNIFTY has insufficient OI: Max Call OI = {max_call_oi}, Max Put OI = {max_put_oi}")

            return has_sufficient_oi
        except Exception as e:
            logger.error(f"Error checking FINNIFTY OI: {e}")
            return False

    def _fetch_market_data(self) -> None:
        """Fetch the latest market data for all symbols from the database."""
        global SYMBOLS

        try:
            # Always fetch data for ALL_SYMBOLS to check conditions
            for symbol in ALL_SYMBOLS:
                query = """
                SELECT * FROM option_data_need_live
                WHERE symbol = %s
                LIMIT 1
                """
                self.cursor.execute(query, (symbol,))
                data = self.cursor.fetchone()

                if data:
                    self.market_data[symbol] = data

                    # Store previous spot price if not already set
                    if self.prev_spot_prices.get(symbol) is None:
                        self.prev_spot_prices[symbol] = float(data['Spot_LTP'])

                    # Store spot prices in history for outbound entry checks
                    current_spot = float(data['Spot_LTP'])
                    self.outbound_spot_history[symbol].append(current_spot)

                    # Keep only the last 5 spot prices in history (for 3-4 seconds of history)
                    if len(self.outbound_spot_history[symbol]) > 5:
                        self.outbound_spot_history[symbol].pop(0)

                    # Update previous outbound spot price every 3-4 seconds
                    current_time = datetime.datetime.now()
                    if self.outbound_mode and (current_time - self.last_outbound_history_update).total_seconds() >= 3:
                        # Use the oldest spot price in history as the previous spot price
                        if len(self.outbound_spot_history[symbol]) > 0:
                            self.prev_outbound_spot_prices[symbol] = self.outbound_spot_history[symbol][0]
                        else:
                            self.prev_outbound_spot_prices[symbol] = current_spot

                        # Update the timestamp for the next update
                        if symbol == SYMBOLS[-1]:  # Only update timestamp after processing the last symbol
                            self.last_outbound_history_update = current_time

                    logger.debug(f"Fetched market data for {symbol}: Spot price: {data['Spot_LTP']}")
                else:
                    logger.warning(f"No market data found for {symbol}")

            # After fetching all data, check if FINNIFTY should be included
            if "FINNIFTY" in ALL_SYMBOLS:
                finnifty_has_sufficient_oi = self._check_finnifty_oi()

                # Update SYMBOLS list based on FINNIFTY OI
                if finnifty_has_sufficient_oi and "FINNIFTY" not in SYMBOLS:
                    SYMBOLS.append("FINNIFTY")
                    logger.info("Added FINNIFTY to trading symbols due to sufficient OI")
                elif not finnifty_has_sufficient_oi and "FINNIFTY" in SYMBOLS:
                    SYMBOLS.remove("FINNIFTY")
                    logger.info("Removed FINNIFTY from trading symbols due to insufficient OI")

            self.conn.commit()
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            # Reconnect to database if connection was lost
            self._connect_to_db()

    def _get_option_strikes(self, symbol: str) -> Tuple[float, float]:
        """
        Get the strike prices for CE and PE for a given symbol.
        For CE, we use ATM-2 (two strikes below ATM).
        For PE, we use ATM+2 (two strikes above ATM).
        Returns a tuple of (CE_strike, PE_strike)
        All strikes are rounded to the nearest multiple of 100.
        """
        try:
            # Get the spot price
            spot_price = float(self.market_data[symbol]['Spot_LTP'])

            # Get all available strikes for this symbol
            query = """
            SELECT DISTINCT Strike FROM option_data_live
            WHERE symbol = %s
            ORDER BY Strike
            """
            self.cursor.execute(query, (symbol,))
            strikes = [float(row['Strike']) for row in self.cursor.fetchall()]

            if not strikes:
                logger.warning(f"No strikes found for {symbol}")
                return None, None

            # Filter strikes to only include multiples of 100
            strikes_100 = [strike for strike in strikes if strike % 100 == 0]

            # If no multiples of 100 are found, round all strikes to nearest 100
            if not strikes_100:
                logger.warning(f"No strikes found that are multiples of 100 for {symbol}. Rounding available strikes.")
                strikes_100 = [round_to_nearest_100(strike) for strike in strikes]
                strikes_100 = sorted(list(set(strikes_100)))  # Remove duplicates and sort

            if not strikes_100:
                logger.warning(f"Still no valid strikes found for {symbol} after rounding")
                return None, None

            # Use the filtered strikes for further processing
            strikes = strikes_100

            # Find the ATM strike (closest to spot price)
            atm_strike = min(strikes, key=lambda x: abs(x - spot_price))
            atm_index = strikes.index(atm_strike)

            # Get ATM-2 strike for CE (two strikes below ATM)
            ce_strike = None
            if atm_index >= 2:
                ce_strike = strikes[atm_index - 1]
            elif atm_index == 1:
                ce_strike = strikes[atm_index - 1]  # Use ATM-1 if we can't go down 2 strikes
            else:
                ce_strike = atm_strike  # Use ATM if we can't go down at all

            # Get ATM+2 strike for PE (two strikes above ATM)
            pe_strike = None
            if atm_index + 2 < len(strikes):
                pe_strike = strikes[atm_index + 1]
            elif atm_index + 1 < len(strikes):
                pe_strike = strikes[atm_index + 1]  # Use ATM+1 if we can't go up 2 strikes
            else:
                pe_strike = atm_strike  # Use ATM if we can't go up at all

            # Ensure strikes are multiples of 100 (should already be the case, but double-check)
            if ce_strike and ce_strike % 100 != 0:
                ce_strike = round_to_nearest_100(ce_strike)
                logger.info(f"{symbol} - Rounded CE strike to nearest 100: {ce_strike}")

            if pe_strike and pe_strike % 100 != 0:
                pe_strike = round_to_nearest_100(pe_strike)
                logger.info(f"{symbol} - Rounded PE strike to nearest 100: {pe_strike}")

            logger.info(f"{symbol} - Spot: {spot_price}, ATM: {atm_strike}, CE (ATM-2): {ce_strike}, PE (ATM+2): {pe_strike}")

            return ce_strike, pe_strike
        except Exception as e:
            logger.error(f"Error getting option strikes for {symbol}: {e}")
            return None, None

    def _check_resistance_crossover(self, symbol: str) -> Tuple[bool, bool]:
        """
        Check if the spot price has crossed the resistance level.
        Returns a tuple of (crossed_above, crossed_below)
        """
        try:
            data = self.market_data[symbol]
            current_spot = float(data['Spot_LTP'])
            prev_spot = self.prev_spot_prices[symbol]

            # Determine resistance based on priority
            if self.is_expiry_day:
                # On expiry day, MAX_CALL_OI takes priority
                resistance = float(data['Max_Call_OI_Strike'])
            else:
                # On normal days, MAX_CALL_CHANGE_IN_OI takes priority
                resistance = float(data['Max_Call_Change_in_OI_Strike'])

            # Apply buffer (25 points for BANKNIFTY, 10 points for others)
            buffer, _ = get_buffer_and_stoploss(symbol)
            resistance_with_buffer = resistance - buffer

            # Check for crossovers
            crossed_above = prev_spot is not None and prev_spot < resistance_with_buffer and current_spot >= resistance_with_buffer
            crossed_below = prev_spot is not None and prev_spot > resistance_with_buffer and current_spot <= resistance_with_buffer

            if crossed_above:
                logger.info(f"{symbol} - Spot price crossed resistance from below to above: {prev_spot} -> {current_spot}, "
                           f"Resistance: {resistance}, With buffer: {resistance_with_buffer}")

            if crossed_below:
                logger.info(f"{symbol} - Spot price crossed resistance from above to below: {prev_spot} -> {current_spot}, "
                           f"Resistance: {resistance}, With buffer: {resistance_with_buffer}")

            return crossed_above, crossed_below
        except Exception as e:
            logger.error(f"Error checking resistance crossover for {symbol}: {e}")
            return False, False

    def _check_support_crossover(self, symbol: str) -> Tuple[bool, bool]:
        """
        Check if the spot price has crossed the support level.
        Returns a tuple of (crossed_above, crossed_below)
        """
        try:
            data = self.market_data[symbol]
            current_spot = float(data['Spot_LTP'])
            prev_spot = self.prev_spot_prices[symbol]

            # Determine support based on priority
            if self.is_expiry_day:
                # On expiry day, MAX_PUT_OI takes priority
                support = float(data['Max_Put_OI_Strike'])
            else:
                # On normal days, MAX_PUT_CHANGE_IN_OI takes priority
                support = float(data['Max_Put_Change_in_OI_Strike'])

            # Apply buffer (25 points for BANKNIFTY, 10 points for others)
            buffer, _ = get_buffer_and_stoploss(symbol)
            support_with_buffer = support + buffer

            # Check for crossovers
            crossed_above = prev_spot is not None and prev_spot < support_with_buffer and current_spot >= support_with_buffer
            crossed_below = prev_spot is not None and prev_spot > support_with_buffer and current_spot <= support_with_buffer

            if crossed_above:
                logger.info(f"{symbol} - Spot price crossed support from below to above: {prev_spot} -> {current_spot}, "
                           f"Support: {support}, With buffer: {support_with_buffer}")

            if crossed_below:
                logger.info(f"{symbol} - Spot price crossed support from above to below: {prev_spot} -> {current_spot}, "
                           f"Support: {support}, With buffer: {support_with_buffer}")

            return crossed_above, crossed_below
        except Exception as e:
            logger.error(f"Error checking support crossover for {symbol}: {e}")
            return False, False

    def _is_any_symbol_near_opposite_level(self, symbol: str, option_type: str) -> bool:
        """
        Check if any other symbol is near its opposite level.

        For CE (calls), check if any other symbol is near its resistance.
        For PE (puts), check if any other symbol is near its support.

        Returns True if any symbol is near its opposite level, False otherwise.
        """
        try:
            # Skip check if we don't have market data for all symbols
            for other_symbol in SYMBOLS:
                if other_symbol != symbol and other_symbol not in self.market_data:
                    logger.warning(f"Missing market data for {other_symbol}, skipping opposite level check")
                    return False

            # For each symbol other than the one we're checking
            for other_symbol in SYMBOLS:
                # if other_symbol == symbol:
                #     continue

                data = self.market_data[other_symbol]
                current_spot = float(data['Spot_LTP'])

                # Determine support and resistance based on priority
                if self.is_expiry_day:
                    # On expiry day, MAX_CALL_OI and MAX_PUT_OI take priority
                    support = float(data['Max_Put_OI_Strike'])
                    resistance = float(data['Max_Call_OI_Strike'])
                else:
                    # On normal days, MAX_CALL_CHANGE_IN_OI and MAX_PUT_CHANGE_IN_OI take priority
                    support = float(data['Max_Put_Change_in_OI_Strike'])
                    resistance = float(data['Max_Call_Change_in_OI_Strike'])

                # Get the opposite buffer for this symbol
                opposite_buffer = get_opposite_buffer(other_symbol)

                if option_type == "CE":
                    # For CE (calls), check if the other symbol is near its resistance
                    if current_spot >= (resistance - opposite_buffer):
                        logger.info(f"Cannot buy CE for {symbol} because {other_symbol} is near its resistance: "
                                   f"Spot: {current_spot}, Resistance: {resistance}, Buffer: {opposite_buffer}")
                        return True
                else:  # PE
                    # For PE (puts), check if the other symbol is near its support
                    if current_spot <= (support + opposite_buffer):
                        logger.info(f"Cannot buy PE for {symbol} because {other_symbol} is near its support: "
                                   f"Spot: {current_spot}, Support: {support}, Buffer: {opposite_buffer}")
                        return True

            return False
        except Exception as e:
            logger.error(f"Error checking opposite levels: {e}")
            return False

    def _calculate_outbound_levels(self):
        """
        Calculate outbound trade support and resistance levels for all symbols
        based on stored spot prices.
        """
        for symbol in SYMBOLS:
            if self.stored_spot_prices[symbol] is not None:
                # Calculate outbound trade support (closest hundred/500 below stored spot price)
                if symbol == "BANKNIFTY":
                    # Find closest 500 below stored spot price
                    self.outbound_trade_support[symbol] = math.floor(self.stored_spot_prices[symbol] / 500) * 500
                    # Find closest 500 above stored spot price
                    self.outbound_trade_resistance[symbol] = math.ceil(self.stored_spot_prices[symbol] / 500) * 500
                else:
                    # Find closest 100 below stored spot price
                    self.outbound_trade_support[symbol] = math.floor(self.stored_spot_prices[symbol] / 100) * 100
                    # Find closest 100 above stored spot price
                    self.outbound_trade_resistance[symbol] = math.ceil(self.stored_spot_prices[symbol] / 100) * 100

                logger.info(f"{symbol} - Outbound levels calculated: Support: {self.outbound_trade_support[symbol]}, "
                           f"Resistance: {self.outbound_trade_resistance[symbol]}, "
                           f"Based on stored spot price: {self.stored_spot_prices[symbol]}")

    def _check_outbound_entry_conditions(self) -> Tuple[bool, bool, str]:
        """
        Check if any symbol has crossed its outbound trade level.
        Returns a tuple of (ce_outbound_signal, pe_outbound_signal, trigger_symbol)
        """
        ce_outbound_signal = False
        pe_outbound_signal = False
        trigger_symbol = None

        # Only check if we're in outbound mode
        if not self.outbound_mode:
            return ce_outbound_signal, pe_outbound_signal, trigger_symbol

        for symbol in SYMBOLS:
            if (symbol not in self.market_data or
                self.prev_outbound_spot_prices[symbol] is None or
                self.outbound_trade_support[symbol] is None or
                self.outbound_trade_resistance[symbol] is None):
                continue

            current_spot = float(self.market_data[symbol]['Spot_LTP'])
            prev_spot = self.prev_outbound_spot_prices[symbol]

            # Get buffer for this symbol (25 for BANKNIFTY, 10 for others)
            buffer, _ = get_buffer_and_stoploss(symbol)

            # Log the time difference between updates
            time_diff = (datetime.datetime.now() - self.last_outbound_history_update).total_seconds()
            logger.info(f"Time since last outbound history update: {time_diff:.2f} seconds")

            # Log the history of spot prices
            history_str = ", ".join([f"{price:.2f}" for price in self.outbound_spot_history[symbol]])
            logger.info(f"{symbol} - Spot price history (oldest to newest): [{history_str}]")

            # Check for outbound support crossover (for CE buy)
            if self.outbound_direction == "SUPPORT":
                # Apply buffer to outbound support level
                outbound_support_with_buffer = self.outbound_trade_support[symbol] + buffer

                # Log detailed comparison for debugging
                logger.info(f"{symbol} - SUPPORT CHECK - Previous spot ({prev_spot:.2f}) vs Current spot ({current_spot:.2f}), "
                           f"Outbound support with buffer: {outbound_support_with_buffer:.2f}, "
                           f"Difference: prev_to_threshold={outbound_support_with_buffer-prev_spot:.2f}, "
                           f"current_to_threshold={current_spot-outbound_support_with_buffer:.2f}")

                crossed_above = (prev_spot < outbound_support_with_buffer and
                                current_spot >= outbound_support_with_buffer)

                if crossed_above:
                    ce_outbound_signal = True
                    trigger_symbol = symbol
                    logger.info(f"{symbol} - Outbound support crossed from below to above: {prev_spot:.2f} -> {current_spot:.2f}, "
                               f"Outbound support: {self.outbound_trade_support[symbol]:.2f}, "
                               f"With buffer: {outbound_support_with_buffer:.2f}")
                    break

            # Check for outbound resistance crossover (for PE buy)
            elif self.outbound_direction == "RESISTANCE":
                # Apply buffer to outbound resistance level
                outbound_resistance_with_buffer = self.outbound_trade_resistance[symbol] - buffer

                # Log detailed comparison for debugging
                logger.info(f"{symbol} - RESISTANCE CHECK - Previous spot ({prev_spot:.2f}) vs Current spot ({current_spot:.2f}), "
                           f"Outbound resistance with buffer: {outbound_resistance_with_buffer:.2f}, "
                           f"Difference: prev_to_threshold={prev_spot-outbound_resistance_with_buffer:.2f}, "
                           f"current_to_threshold={outbound_resistance_with_buffer-current_spot:.2f}")

                crossed_below = (prev_spot > outbound_resistance_with_buffer and
                                current_spot <= outbound_resistance_with_buffer)

                if crossed_below:
                    pe_outbound_signal = True
                    trigger_symbol = symbol
                    logger.info(f"{symbol} - Outbound resistance crossed from above to below: {prev_spot:.2f} -> {current_spot:.2f}, "
                               f"Outbound resistance: {self.outbound_trade_resistance[symbol]:.2f}, "
                               f"With buffer: {outbound_resistance_with_buffer:.2f}")
                    break

        return ce_outbound_signal, pe_outbound_signal, trigger_symbol

    def _check_outbound_conditions(self) -> None:
        """
        Check if any symbol has moved beyond its support/resistance level
        to trigger outbound mode.
        """
        # Skip if already in outbound mode
        if self.outbound_mode:
            return

        for symbol in SYMBOLS:
            if symbol not in self.market_data:
                continue

            data = self.market_data[symbol]
            current_spot = float(data['Spot_LTP'])

            # Determine support and resistance based on priority
            if self.is_expiry_day:
                # On expiry day, MAX_CALL_OI and MAX_PUT_OI take priority
                support = float(data['Max_Put_OI_Strike'])
                resistance = float(data['Max_Call_OI_Strike'])
            else:
                # On normal days, MAX_CALL_CHANGE_IN_OI and MAX_PUT_CHANGE_IN_OI take priority
                support = float(data['Max_Put_Change_in_OI_Strike'])
                resistance = float(data['Max_Call_Change_in_OI_Strike'])

            # Calculate range between support and resistance
            range_points = resistance - support
            min_range = get_min_range(symbol)

            # Only check symbols with sufficient range
            if range_points >= min_range:
                print('inside check',current_spot , support)
                # Check if spot is below support (for CE outbound)
                if current_spot < support:
                    logger.info(f"{symbol} - Spot price below support: {current_spot} < {support}. Entering outbound mode for SUPPORT.")
                    self.outbound_mode = True
                    self.outbound_direction = "SUPPORT"
                    self.outbound_trigger_time = datetime.datetime.now()
                    self.outbound_trigger_symbol = symbol

                    # Store spot prices for all symbols
                    for s in SYMBOLS:
                        if s in self.market_data:
                            self.stored_spot_prices[s] = float(self.market_data[s]['Spot_LTP'])
                            self.prev_outbound_spot_prices[s] = self.stored_spot_prices[s]

                    # Calculate outbound levels
                    self._calculate_outbound_levels()
                    return

                # Check if spot is above resistance (for PE outbound)
                elif current_spot > resistance:
                    logger.info(f"{symbol} - Spot price above resistance: {current_spot} > {resistance}. Entering outbound mode for RESISTANCE.")
                    self.outbound_mode = True
                    self.outbound_direction = "RESISTANCE"
                    self.outbound_trigger_time = datetime.datetime.now()
                    self.outbound_trigger_symbol = symbol

                    # Store spot prices for all symbols
                    for s in SYMBOLS:
                        if s in self.market_data:
                            self.stored_spot_prices[s] = float(self.market_data[s]['Spot_LTP'])
                            self.prev_outbound_spot_prices[s] = self.stored_spot_prices[s]

                    # Calculate outbound levels
                    self._calculate_outbound_levels()
                    return

    def _check_outbound_exit_conditions(self) -> None:
        """
        Check if we should exit outbound mode because the conditions are no longer valid.
        This happens when the spot price moves back within the normal support/resistance range.
        """
        if not self.outbound_mode:
            return

        # Check the original trigger symbol to see if it's still in outbound condition
        trigger_symbol = self.outbound_trigger_symbol
        if not trigger_symbol or trigger_symbol not in self.market_data:
            return

        data = self.market_data[trigger_symbol]
        current_spot = float(data['Spot_LTP'])

        # Determine support and resistance based on priority
        if self.is_expiry_day:
            # On expiry day, MAX_CALL_OI and MAX_PUT_OI take priority
            support = float(data['Max_Put_OI_Strike'])
            resistance = float(data['Max_Call_OI_Strike'])
        else:
            # On normal days, MAX_CALL_CHANGE_IN_OI and MAX_PUT_CHANGE_IN_OI take priority
            support = float(data['Max_Put_Change_in_OI_Strike'])
            resistance = float(data['Max_Call_Change_in_OI_Strike'])

        # Check if we should exit outbound mode
        should_exit_outbound = False

        if self.outbound_direction == "SUPPORT":
            # If we were in outbound mode for support (spot was below support),
            # exit if spot is now back above support
            if current_spot >= support:
                should_exit_outbound = True
                logger.info(f"{trigger_symbol} - Exiting outbound mode: Spot price moved back above support: {current_spot} >= {support}")

        elif self.outbound_direction == "RESISTANCE":
            # If we were in outbound mode for resistance (spot was above resistance),
            # exit if spot is now back below resistance
            if current_spot <= resistance:
                should_exit_outbound = True
                logger.info(f"{trigger_symbol} - Exiting outbound mode: Spot price moved back below resistance: {current_spot} <= {resistance}")

        if should_exit_outbound:
            logger.info(f"Exiting outbound mode. Waiting for new outbound conditions to form.")
            # Reset outbound mode
            self.outbound_mode = False
            self.outbound_direction = None
            self.outbound_trigger_time = None
            self.outbound_trigger_symbol = None
            self.stored_spot_prices = {symbol: None for symbol in ALL_SYMBOLS}
            self.outbound_trade_support = {symbol: None for symbol in ALL_SYMBOLS}
            self.outbound_trade_resistance = {symbol: None for symbol in ALL_SYMBOLS}

    def _check_stoploss_hit(self, symbol: str) -> Tuple[bool, bool]:
        """
        Check if stoploss is hit for any position.
        Returns a tuple of (ce_stoploss_hit, pe_stoploss_hit)
        """
        try:
            data = self.market_data[symbol]
            current_spot = float(data['Spot_LTP'])

            ce_position = self.positions[symbol]["CE"]
            pe_position = self.positions[symbol]["PE"]

            ce_stoploss_hit = False
            pe_stoploss_hit = False

            # Check CE stoploss - 10 points below support
            if ce_position["active"]:
                # Use the support value stored at entry time
                entry_support = ce_position.get("entry_support")
                if entry_support is None:  # Fallback for backward compatibility
                    # Determine support based on priority
                    if self.is_expiry_day:
                        # On expiry day, MAX_PUT_OI takes priority
                        entry_support = float(data['Max_Put_OI_Strike'])
                    else:
                        # On normal days, MAX_PUT_CHANGE_IN_OI takes priority
                        entry_support = float(data['Max_Put_Change_in_OI_Strike'])

                # Stoploss based on support level at entry time (25 points for BANKNIFTY, 10 points for others)
                _, stoploss_level = get_buffer_and_stoploss(symbol)
                support_stoploss = entry_support - stoploss_level

                if current_spot <= support_stoploss:
                    logger.info(f"{symbol} - CE stoploss hit at {current_spot}. Entry support: {entry_support}, Stoploss level: {support_stoploss}")
                    ce_stoploss_hit = True

            # Check PE stoploss - 10 points above resistance
            if pe_position["active"]:
                # Use the resistance value stored at entry time
                entry_resistance = pe_position.get("entry_resistance")
                if entry_resistance is None:  # Fallback for backward compatibility
                    # Determine resistance based on priority
                    if self.is_expiry_day:
                        # On expiry day, MAX_CALL_OI takes priority
                        entry_resistance = float(data['Max_Call_OI_Strike'])
                    else:
                        # On normal days, MAX_CALL_CHANGE_IN_OI takes priority
                        entry_resistance = float(data['Max_Call_Change_in_OI_Strike'])

                # Stoploss based on resistance level at entry time (25 points for BANKNIFTY, 10 points for others)
                _, stoploss_level = get_buffer_and_stoploss(symbol)
                resistance_stoploss = entry_resistance + stoploss_level

                if current_spot >= resistance_stoploss:
                    logger.info(f"{symbol} - PE stoploss hit at {current_spot}. Entry resistance: {entry_resistance}, Stoploss level: {resistance_stoploss}")
                    pe_stoploss_hit = True

            return ce_stoploss_hit, pe_stoploss_hit
        except Exception as e:
            logger.error(f"Error checking stoploss for {symbol}: {e}")
            return False, False

    def _buy_ce(self, symbol: str, reason: str) -> None:
        """Buy CE option for the given symbol using Zerodha API."""
        # If we already have a CE position, skip (no replacement needed for same type)
        if self.positions[symbol]["CE"]["active"]:
            logger.info(f"{symbol} - CE position already active, skipping buy")
            return

        # If we have an active PE position, sell it first (position replacement)
        if self.positions[symbol]["PE"]["active"]:
            logger.info(f"{symbol} - Selling existing PE position before buying CE")
            self._sell_pe(symbol, f"Position replacement: Selling PE to buy CE - {reason}")

        # Check if any other symbol is near its resistance (opposite level for CE)
        if self._is_any_symbol_near_opposite_level(symbol, "CE"):
            logger.info(f"{symbol} - Cannot buy CE because another symbol is near its resistance")
            return

        # Get ATM-2 strike for CE
        ce_strike, _ = self._get_option_strikes(symbol)
        if not ce_strike:
            logger.warning(f"{symbol} - Could not determine ATM-2 strike for CE, skipping buy")
            return

        # Check if this is an outbound trade
        is_outbound_trade = "Outbound support crossed" in reason

        # Get current market data
        data = self.market_data[symbol]
        current_spot = float(data['Spot_LTP'])

        # Determine support and resistance based on priority
        if self.is_expiry_day:
            support = float(data['Max_Put_OI_Strike'])
            resistance = float(data['Max_Call_OI_Strike'])
        else:
            support = float(data['Max_Put_Change_in_OI_Strike'])
            resistance = float(data['Max_Call_Change_in_OI_Strike'])

        # Get option price from option_data_live table
        try:
            query = """
            SELECT CE_LTP FROM option_data_live
            WHERE symbol = %s AND Strike = %s
            ORDER BY timestamp DESC LIMIT 1
            """
            self.cursor.execute(query, (symbol, ce_strike))
            result = self.cursor.fetchone()
            option_price = float(result['CE_LTP']) if result else 0.0
        except Exception as e:
            logger.error(f"Error getting option price for {symbol} CE at strike {ce_strike}: {e}")
            option_price = 0.0

        # Extract trigger symbol from reason (format: "Support crossed from below to above in SYMBOL")
        trigger_symbol = None
        if "in " in reason:
            trigger_symbol = reason.split("in ")[-1]

        # Check if this is an outbound trade
        is_outbound_trade = "Outbound support crossed" in reason

        # For outbound trades, use outbound support for stoploss calculation
        if is_outbound_trade and self.outbound_trade_support.get(symbol) is not None:
            # Use outbound support instead of normal support for stoploss
            entry_support = self.outbound_trade_support[symbol]
            logger.info(f"{symbol} - Using outbound support for stoploss: {entry_support}")

            # For outbound trades, we use (regular support - buffer) as target
            buffer, _ = get_buffer_and_stoploss(symbol)
            target_level = support - buffer
            logger.info(f"{symbol} - Using (regular support - buffer) for target: {support} - {buffer} = {target_level}")

            # Set the is_outbound_trade flag
            is_outbound_trade = True
        else:
            entry_support = support
            is_outbound_trade = False

        # Generate trading symbol for Zerodha
        trading_symbol = self._generate_trading_symbol(symbol, ce_strike, "CE")
        if not trading_symbol:
            logger.error(f"{symbol} - Failed to generate trading symbol for CE at strike {ce_strike}")
            return

        # Get lot size for the symbol
        lot_size = get_lot_size(symbol)

        # Place order with Zerodha
        try:
            order_response = self.kite.place_order(
                variety=self.kite.VARIETY_REGULAR,
                exchange=self.kite.EXCHANGE_NFO,
                tradingsymbol=trading_symbol,
                transaction_type=self.kite.TRANSACTION_TYPE_BUY,
                quantity=lot_size,
                product=self.kite.PRODUCT_NRML,  # Intraday
                order_type=self.kite.ORDER_TYPE_MARKET,  # Market order
            )

            order_id = order_response.get("data", {}).get("order_id")
            if not order_id:
                logger.error(f"{symbol} - Failed to get order ID from Zerodha response: {order_response}")
                return

            logger.info(f"{symbol} - Zerodha order placed successfully with order ID: {order_id}")

            # Record the position with support and resistance at entry time
            self.positions[symbol]["CE"] = {
                "active": True,
                "strike": ce_strike,
                "entry_price": option_price,
                "entry_time": datetime.datetime.now(),
                "entry_support": entry_support,  # Store support at entry time (normal or outbound)
                "entry_resistance": resistance,  # Store resistance at entry time (always regular resistance)
                "trigger_symbol": trigger_symbol,  # Store which symbol triggered this position
                "is_outbound_trade": is_outbound_trade,  # Flag to indicate if this is an outbound trade
                "order_id": order_id,  # Store Zerodha order ID
                "tradingsymbol": trading_symbol  # Store Zerodha trading symbol
            }

            # Log the order in ORDER_LOG table
            try:
                now = datetime.datetime.now()
                self.cursor.execute("""
                INSERT INTO ORDER_LOG (
                    timestamp, symbol, order_type, option_type, strike,
                    spot_price, option_price, reason, support, resistance, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    now, symbol, "BUY", "CE", ce_strike,
                    current_spot, option_price, reason, support, resistance, "EXECUTED"
                ))
                self.conn.commit()
            except Exception as e:
                logger.error(f"Error logging order: {e}")

            logger.info(f"{symbol} - Bought CE at strike {ce_strike}, spot price: {current_spot}, option price: {option_price}, reason: {reason}")
            message = f"{symbol} - Bought CE at strike {ce_strike}, spot price: {current_spot}, option price: {option_price}, reason: {reason}, order ID: {order_id}"
            send_telegram_message(message)

        except Exception as e:
            logger.error(f"{symbol} - Error placing CE buy order with Zerodha: {e}")
            message = f"{symbol} - Failed to buy {trading_symbol} CE at strike {ce_strike}: {e}"
            send_telegram_message(message)

    def _sell_ce(self, symbol: str, reason: str) -> None:
        """Sell CE option for the given symbol using Zerodha API."""
        if not self.positions[symbol]["CE"]["active"]:
            logger.info(f"{symbol} - No active CE position to sell")
            return

        # Get position details
        ce_strike = self.positions[symbol]["CE"]["strike"]
        entry_price = self.positions[symbol]["CE"]["entry_price"]
        trading_symbol = self.positions[symbol]["CE"].get("tradingsymbol")

        # If we don't have a trading symbol (could happen if position was synced from Zerodha)
        if not trading_symbol:
            trading_symbol = self._generate_trading_symbol(symbol, ce_strike, "CE")
            if not trading_symbol:
                logger.error(f"{symbol} - Failed to generate trading symbol for CE at strike {ce_strike}")
                return

        # Get current market data
        data = self.market_data[symbol]
        current_spot = float(data['Spot_LTP'])

        # Determine support and resistance based on priority
        if self.is_expiry_day:
            support = float(data['Max_Put_OI_Strike'])
            resistance = float(data['Max_Call_OI_Strike'])
        else:
            support = float(data['Max_Put_Change_in_OI_Strike'])
            resistance = float(data['Max_Call_Change_in_OI_Strike'])

        # Get current option price from option_data_live table
        try:
            query = """
            SELECT CE_LTP FROM option_data_live
            WHERE symbol = %s AND Strike = %s
            ORDER BY timestamp DESC LIMIT 1
            """
            self.cursor.execute(query, (symbol, ce_strike))
            result = self.cursor.fetchone()
            current_option_price = float(result['CE_LTP']) if result else 0.0
        except Exception as e:
            logger.error(f"Error getting option price for {symbol} CE at strike {ce_strike}: {e}")
            current_option_price = 0.0

        # Get lot size for the symbol
        lot_size = get_lot_size(symbol)

        # Place sell order with Zerodha
        try:
            order_response = self.kite.place_order(
                variety=self.kite.VARIETY_REGULAR,
                exchange=self.kite.EXCHANGE_NFO,
                tradingsymbol=trading_symbol,
                transaction_type=self.kite.TRANSACTION_TYPE_SELL,
                quantity=lot_size,
                product=self.kite.PRODUCT_NRML,  # Intraday
                order_type=self.kite.ORDER_TYPE_MARKET,  # Market order
            )

            order_id = order_response.get("data", {}).get("order_id")
            if not order_id:
                logger.error(f"{symbol} - Failed to get order ID from Zerodha response: {order_response}")
                return

            logger.info(f"{symbol} - Zerodha sell order placed successfully with order ID: {order_id}")

            # Calculate P&L (price difference * lot size)
            price_diff = current_option_price - entry_price
            pnl = price_diff * lot_size

            logger.info(f"{symbol} - PnL calculation: ({current_option_price} - {entry_price}) * {lot_size} = {pnl}")

            # Log the order in ORDER_LOG table
            try:
                now = datetime.datetime.now()
                self.cursor.execute("""
                INSERT INTO ORDER_LOG (
                    timestamp, symbol, order_type, option_type, strike,
                    spot_price, option_price, reason, support, resistance, pnl, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    now, symbol, "SELL", "CE", ce_strike,
                    current_spot, current_option_price, reason, support, resistance, pnl, "EXECUTED"
                ))
                self.conn.commit()
            except Exception as e:
                logger.error(f"Error logging order: {e}")

            # Close the position
            self.positions[symbol]["CE"] = {
                "active": False,
                "strike": None,
                "entry_price": None,
                "entry_time": None,
                "entry_support": None,
                "entry_resistance": None,
                "trigger_symbol": None,
                "is_outbound_trade": False,
                "order_id": None,
                "tradingsymbol": None
            }

            logger.info(f"{symbol} - Sold CE at strike {ce_strike}, entry price: {entry_price}, exit price: {current_option_price}, P&L: {price_diff} * {lot_size} = {pnl}, reason: {reason}")
            message = f"{symbol} - Sold CE at strike {ce_strike}, entry price: {entry_price}, exit price: {current_option_price}, P&L: {price_diff} * {lot_size} = {pnl}, reason: {reason}, order ID: {order_id}"
            send_telegram_message(message)

        except Exception as e:
            logger.error(f"{symbol} - Error placing CE sell order with Zerodha: {e}")
            message = f"{symbol} - Failed to sell CE at strike {ce_strike}: {e}"
            send_telegram_message(message)

    def _buy_pe(self, symbol: str, reason: str) -> None:
        """Buy PE option for the given symbol using Zerodha API."""
        # If we already have a PE position, skip (no replacement needed for same type)
        if self.positions[symbol]["PE"]["active"]:
            logger.info(f"{symbol} - PE position already active, skipping buy")
            return

        # If we have an active CE position, sell it first (position replacement)
        if self.positions[symbol]["CE"]["active"]:
            logger.info(f"{symbol} - Selling existing CE position before buying PE")
            self._sell_ce(symbol, f"Position replacement: Selling CE to buy PE - {reason}")

        # Check if any other symbol is near its support (opposite level for PE)
        if self._is_any_symbol_near_opposite_level(symbol, "PE"):
            logger.info(f"{symbol} - Cannot buy PE because another symbol is near its support")
            return

        # Get ATM+2 strike for PE
        _, pe_strike = self._get_option_strikes(symbol)
        if not pe_strike:
            logger.warning(f"{symbol} - Could not determine ATM+2 strike for PE, skipping buy")
            return

        # Check if this is an outbound trade
        is_outbound_trade = "Outbound resistance crossed" in reason

        # Get current market data
        data = self.market_data[symbol]
        current_spot = float(data['Spot_LTP'])

        # Determine support and resistance based on priority
        if self.is_expiry_day:
            support = float(data['Max_Put_OI_Strike'])
            resistance = float(data['Max_Call_OI_Strike'])
        else:
            support = float(data['Max_Put_Change_in_OI_Strike'])
            resistance = float(data['Max_Call_Change_in_OI_Strike'])

        # Get option price from option_data_live table
        try:
            query = """
            SELECT PE_LTP FROM option_data_live
            WHERE symbol = %s AND Strike = %s
            ORDER BY timestamp DESC LIMIT 1
            """
            self.cursor.execute(query, (symbol, pe_strike))
            result = self.cursor.fetchone()
            option_price = float(result['PE_LTP']) if result else 0.0
        except Exception as e:
            logger.error(f"Error getting option price for {symbol} PE at strike {pe_strike}: {e}")
            option_price = 0.0

        # Extract trigger symbol from reason (format: "Resistance crossed from above to below in SYMBOL")
        trigger_symbol = None
        if "in " in reason:
            trigger_symbol = reason.split("in ")[-1]

        # Check if this is an outbound trade
        is_outbound_trade = "Outbound resistance crossed" in reason

        # For outbound trades, use outbound resistance for stoploss calculation
        if is_outbound_trade and self.outbound_trade_resistance.get(symbol) is not None:
            # Use outbound resistance instead of normal resistance for stoploss
            entry_resistance = self.outbound_trade_resistance[symbol]
            logger.info(f"{symbol} - Using outbound resistance for stoploss: {entry_resistance}")

            # For outbound trades, we use (regular resistance + buffer) as target
            buffer, _ = get_buffer_and_stoploss(symbol)
            target_level = resistance + buffer
            logger.info(f"{symbol} - Using (regular resistance + buffer) for target: {resistance} + {buffer} = {target_level}")

            # Set the is_outbound_trade flag
            is_outbound_trade = True
        else:
            entry_resistance = resistance
            is_outbound_trade = False

        # Generate trading symbol for Zerodha
        trading_symbol = self._generate_trading_symbol(symbol, pe_strike, "PE")
        if not trading_symbol:
            logger.error(f"{symbol} - Failed to generate trading symbol for PE at strike {pe_strike}")
            return

        # Get lot size for the symbol
        lot_size = get_lot_size(symbol)

        # Place order with Zerodha
        try:
            order_response = self.kite.place_order(
                variety=self.kite.VARIETY_REGULAR,
                exchange=self.kite.EXCHANGE_NFO,
                tradingsymbol=trading_symbol,
                transaction_type=self.kite.TRANSACTION_TYPE_BUY,
                quantity=lot_size,
                product=self.kite.PRODUCT_NRML,  # Intraday
                order_type=self.kite.ORDER_TYPE_MARKET,  # Market order
            )

            order_id = order_response.get("data", {}).get("order_id")
            if not order_id:
                logger.error(f"{symbol} - Failed to get order ID from Zerodha response: {order_response}")
                return

            logger.info(f"{symbol} - Zerodha order placed successfully with order ID: {order_id}")

            # Record the position with support and resistance at entry time
            self.positions[symbol]["PE"] = {
                "active": True,
                "strike": pe_strike,
                "entry_price": option_price,
                "entry_time": datetime.datetime.now(),
                "entry_support": support,  # Store support at entry time (always regular support)
                "entry_resistance": entry_resistance,  # Store resistance at entry time (normal or outbound)
                "trigger_symbol": trigger_symbol,  # Store which symbol triggered this position
                "is_outbound_trade": is_outbound_trade,  # Flag to indicate if this is an outbound trade
                "order_id": order_id,  # Store Zerodha order ID
                "tradingsymbol": trading_symbol  # Store Zerodha trading symbol
            }

            # Log the order in ORDER_LOG table
            try:
                now = datetime.datetime.now()
                self.cursor.execute("""
                INSERT INTO ORDER_LOG (
                    timestamp, symbol, order_type, option_type, strike,
                    spot_price, option_price, reason, support, resistance, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    now, symbol, "BUY", "PE", pe_strike,
                    current_spot, option_price, reason, support, resistance, "EXECUTED"
                ))
                self.conn.commit()
            except Exception as e:
                logger.error(f"Error logging order: {e}")

            logger.info(f"{symbol} - Bought PE at strike {pe_strike}, spot price: {current_spot}, option price: {option_price}, reason: {reason}")
            message = f"{symbol} - Bought PE at strike {pe_strike}, spot price: {current_spot}, option price: {option_price}, reason: {reason}, order ID: {order_id}"
            send_telegram_message(message)

        except Exception as e:
            logger.error(f"{symbol} - Error placing PE buy order with Zerodha: {e}")
            message = f"{symbol} - Failed to buy {trading_symbol} PE at strike {pe_strike}: {e}"
            send_telegram_message(message)

    def _sell_pe(self, symbol: str, reason: str) -> None:
        """Sell PE option for the given symbol using Zerodha API."""
        if not self.positions[symbol]["PE"]["active"]:
            logger.info(f"{symbol} - No active PE position to sell")
            return

        # Get position details
        pe_strike = self.positions[symbol]["PE"]["strike"]
        entry_price = self.positions[symbol]["PE"]["entry_price"]
        trading_symbol = self.positions[symbol]["PE"].get("tradingsymbol")

        # If we don't have a trading symbol (could happen if position was synced from Zerodha)
        if not trading_symbol:
            trading_symbol = self._generate_trading_symbol(symbol, pe_strike, "PE")
            if not trading_symbol:
                logger.error(f"{symbol} - Failed to generate trading symbol for PE at strike {pe_strike}")
                return

        # Get current market data
        data = self.market_data[symbol]
        current_spot = float(data['Spot_LTP'])

        # Determine support and resistance based on priority
        if self.is_expiry_day:
            support = float(data['Max_Put_OI_Strike'])
            resistance = float(data['Max_Call_OI_Strike'])
        else:
            support = float(data['Max_Put_Change_in_OI_Strike'])
            resistance = float(data['Max_Call_Change_in_OI_Strike'])

        # Get current option price from option_data_live table
        try:
            query = """
            SELECT PE_LTP FROM option_data_live
            WHERE symbol = %s AND Strike = %s
            ORDER BY timestamp DESC LIMIT 1
            """
            self.cursor.execute(query, (symbol, pe_strike))
            result = self.cursor.fetchone()
            current_option_price = float(result['PE_LTP']) if result else 0.0
        except Exception as e:
            logger.error(f"Error getting option price for {symbol} PE at strike {pe_strike}: {e}")
            current_option_price = 0.0

        # Get lot size for the symbol
        lot_size = get_lot_size(symbol)

        # Place sell order with Zerodha
        try:
            order_response = self.kite.place_order(
                variety=self.kite.VARIETY_REGULAR,
                exchange=self.kite.EXCHANGE_NFO,
                tradingsymbol=trading_symbol,
                transaction_type=self.kite.TRANSACTION_TYPE_SELL,
                quantity=lot_size,
                product=self.kite.PRODUCT_NRML,  # Intraday
                order_type=self.kite.ORDER_TYPE_MARKET,  # Market order
            )

            order_id = order_response.get("data", {}).get("order_id")
            if not order_id:
                logger.error(f"{symbol} - Failed to get order ID from Zerodha response: {order_response}")
                return

            logger.info(f"{symbol} - Zerodha sell order placed successfully with order ID: {order_id}")

            # Calculate P&L (price difference * lot size)
            price_diff = current_option_price - entry_price
            pnl = price_diff * lot_size

            logger.info(f"{symbol} - PnL calculation: ({current_option_price} - {entry_price}) * {lot_size} = {pnl}")

            # Log the order in ORDER_LOG table
            try:
                now = datetime.datetime.now()
                self.cursor.execute("""
                INSERT INTO ORDER_LOG (
                    timestamp, symbol, order_type, option_type, strike,
                    spot_price, option_price, reason, support, resistance, pnl, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    now, symbol, "SELL", "PE", pe_strike,
                    current_spot, current_option_price, reason, support, resistance, pnl, "EXECUTED"
                ))
                self.conn.commit()
            except Exception as e:
                logger.error(f"Error logging order: {e}")

            # Close the position
            self.positions[symbol]["PE"] = {
                "active": False,
                "strike": None,
                "entry_price": None,
                "entry_time": None,
                "entry_support": None,
                "entry_resistance": None,
                "trigger_symbol": None,
                "is_outbound_trade": False,
                "order_id": None,
                "tradingsymbol": None
            }

            logger.info(f"{symbol} - Sold PE at strike {pe_strike}, entry price: {entry_price}, exit price: {current_option_price}, P&L: {price_diff} * {lot_size} = {pnl}, reason: {reason}")
            message = f"{symbol} - Sold PE at strike {pe_strike}, entry price: {entry_price}, exit price: {current_option_price}, P&L: {price_diff} * {lot_size} = {pnl}, reason: {reason}, order ID: {order_id}"
            send_telegram_message(message)

        except Exception as e:
            logger.error(f"{symbol} - Error placing PE sell order with Zerodha: {e}")
            message = f"{symbol} - Failed to sell PE at strike {pe_strike}: {e}"
            send_telegram_message(message)

    def _square_off_all_positions(self) -> None:
        """Square off all open positions."""
        logger.info("Squaring off all positions at market close")

        for symbol in SYMBOLS:
            if self.positions[symbol]["CE"]["active"]:
                self._sell_ce(symbol, "Market close square-off")

            if self.positions[symbol]["PE"]["active"]:
                self._sell_pe(symbol, "Market close square-off")

    def _process_trading_signals(self) -> None:
        """Process trading signals for all symbols."""
        # Check for signals in each symbol
        resistance_crossed_above = False
        resistance_crossed_below = False
        support_crossed_above = False
        support_crossed_below = False

        # Outbound signals
        ce_outbound_signal = False
        pe_outbound_signal = False
        outbound_trigger_symbol = None

        # Track which symbol triggered the signal
        trigger_symbol = None

        # First, check for outbound conditions (if not already in outbound mode)
        # print('===================================',self.outbound_mode)
        if not self.outbound_mode:
            # print('chcekoutbound')
            self._check_outbound_conditions()
        else:
            # If already in outbound mode, check if we should exit outbound mode
            self._check_outbound_exit_conditions()

        # If in outbound mode, check for outbound entry signals
        if self.outbound_mode:
            ce_outbound_signal, pe_outbound_signal, outbound_trigger_symbol = self._check_outbound_entry_conditions()

            # If we got an outbound signal, reset outbound mode after processing
            if ce_outbound_signal or pe_outbound_signal:
                logger.info(f"Outbound signal detected. Resetting outbound mode.")
                # We'll reset outbound mode after processing the signals

        # Check which symbols have sufficient range between support and resistance
        sufficient_range_exists = False
        symbols_with_sufficient_range = []
        is_return=False


        for symbol in SYMBOLS:
            if not self.market_data.get(symbol):
                continue

            data = self.market_data[symbol]

            # Determine support and resistance based on priority
            if self.is_expiry_day:
                # On expiry day, MAX_CALL_OI and MAX_PUT_OI take priority
                support = float(data['Max_Put_OI_Strike'])
                resistance = float(data['Max_Call_OI_Strike'])
            else:
                # On normal days, MAX_CALL_CHANGE_IN_OI and MAX_PUT_CHANGE_IN_OI take priority
                support = float(data['Max_Put_Change_in_OI_Strike'])
                resistance = float(data['Max_Call_Change_in_OI_Strike'])

            # Calculate range between support and resistance
            range_points = resistance - support
            print(range_points,symbol)

            # Get minimum range requirement for this symbol (201 for BANKNIFTY, 51 for others)
            min_range = get_min_range(symbol)

            if range_points >= min_range:
                sufficient_range_exists = True
                symbols_with_sufficient_range.append(symbol)
                logger.info(f"{symbol} - Sufficient range between support and resistance: {range_points} points (required: {min_range})")
            else:
                logger.info(f"{symbol} - Insufficient range between support and resistance: {range_points} points (required: {min_range})")
                is_return =True

        # Find symbols with open trades
        symbols_with_open_trades = set()
        for symbol in SYMBOLS:
            if (self.positions[symbol]["CE"]["active"] or
                self.positions[symbol]["PE"]["active"]):
                symbols_with_open_trades.add(symbol)
                is_return = False

        logger.info(f"Symbols with open trades: {symbols_with_open_trades}")
        if is_return:
            return

        # Only skip if there are no symbols with sufficient range AND no open trades
        if not sufficient_range_exists and not symbols_with_open_trades:
            logger.info("No symbol has sufficient range between support and resistance and no open trades. Skipping trading signals.")
            return

        logger.info(f"Symbols with sufficient range: {symbols_with_sufficient_range}")

        # Create a list of symbols to check for crossovers
        # Include symbols with sufficient range OR symbols with open trades
        symbols_to_check = list(set(symbols_with_sufficient_range))

        logger.info(f"Checking crossovers for symbols: {symbols_to_check}")

        # Check for crossovers in symbols with sufficient range OR open trades
        for symbol in symbols_to_check:
            # Skip if no market data
            if not self.market_data.get(symbol):
                continue
            data = self.market_data[symbol]

            # Check resistance crossovers
            res_above, res_below = self._check_resistance_crossover(symbol)
            if res_above and not resistance_crossed_above:
                resistance_crossed_above = True
                trigger_symbol = symbol
            if res_below and not resistance_crossed_below:
                resistance_crossed_below = True
                trigger_symbol = symbol

            # Check support crossovers
            sup_above, sup_below = self._check_support_crossover(symbol)
            if sup_above and not support_crossed_above:
                support_crossed_above = True
                trigger_symbol = symbol
            if sup_below and not support_crossed_below:
                support_crossed_below = True
                trigger_symbol = symbol

            # Check for outbound trades that need to be exited based on target levels
            # For CE outbound trades, check if spot price crosses (support - buffer) from below to above (target)
            # For PE outbound trades, check if spot price crosses (resistance + buffer) from above to below (target)

            # Variables to track if any symbol has hit its target
            if self.positions[symbol]["CE"]["active"] and self.positions[symbol]["CE"].get("is_outbound_trade", False):
                # Get buffer for this symbol
                buffer, _ = get_buffer_and_stoploss(symbol)

                # Get the support level (always regular support)
                if self.is_expiry_day:
                    support = float(data['Max_Put_OI_Strike'])
                else:
                    support = float(data['Max_Put_Change_in_OI_Strike'])

                # Calculate target level: support - buffer
                target_level = support - buffer

                # Check if current spot price has crossed the target level from below to above
                prev_spot = self.prev_spot_prices[symbol]
                current_spot = float(data['Spot_LTP'])

                if prev_spot is not None and prev_spot < target_level and current_spot >= target_level:
                    logger.info(f"{prev_spot}, {target_level} , {current_spot},prev_spot , target_level , current_spot")
                    logger.info(f"{symbol} - CE outbound trade target hit: Spot crossed (support - buffer) from below to above")
                    logger.info(f"{symbol} - Target details: Support: {support}, Buffer: {buffer}, Target level: {target_level}")

                    # Sell all outbound CE positions across all symbols
                    reason = f"Target hit: Spot crossed (support - buffer) from below to above in {symbol}"
                    logger.info(f"Selling all outbound CE positions because {symbol} hit its target")

                    for s in SYMBOLS:
                        if self.positions[s]["CE"]["active"] and self.positions[s]["CE"].get("is_outbound_trade", False):
                            self._sell_ce(s, reason)

            if self.positions[symbol]["PE"]["active"] and self.positions[symbol]["PE"].get("is_outbound_trade", False):
                # Get buffer for this symbol
                buffer, _ = get_buffer_and_stoploss(symbol)

                # Get the resistance level (always regular resistance)
                if self.is_expiry_day:
                    resistance = float(data['Max_Call_OI_Strike'])
                else:
                    resistance = float(data['Max_Call_Change_in_OI_Strike'])

                # Calculate target level: resistance + buffer
                target_level = resistance + buffer

                # Check if current spot price has crossed the target level from above to below
                prev_spot = self.prev_spot_prices[symbol]
                current_spot = float(data['Spot_LTP'])

                if prev_spot is not None and prev_spot > target_level and current_spot <= target_level:
                    logger.info(f"{prev_spot}, {target_level} , {current_spot},prev_spot , target_level , current_spot")
                    logger.info(f"{symbol} - PE outbound trade target hit: Spot crossed (resistance + buffer) from above to below")
                    logger.info(f"{symbol} - Target details: Resistance: {resistance}, Buffer: {buffer}, Target level: {target_level}")

                    # Sell all outbound PE positions across all symbols
                    reason = f"Target hit: Spot crossed (resistance + buffer) from above to below in {symbol}"
                    logger.info(f"Selling all outbound PE positions because {symbol} hit its target")

                    for s in SYMBOLS:
                        if self.positions[s]["PE"]["active"] and self.positions[s]["PE"].get("is_outbound_trade", False):
                            self._sell_pe(s, reason)

        # Check stoploss only for symbols that triggered a buy
        # We need to track which symbols have triggered buys
        ce_trigger_symbols = set()
        pe_trigger_symbols = set()

        # Find all trigger symbols for active positions
        for symbol in SYMBOLS:
            ce_position = self.positions[symbol]["CE"]
            pe_position = self.positions[symbol]["PE"]

            if ce_position["active"] and ce_position["trigger_symbol"]:
                ce_trigger_symbols.add(ce_position["trigger_symbol"])

            if pe_position["active"] and pe_position["trigger_symbol"]:
                pe_trigger_symbols.add(pe_position["trigger_symbol"])

        # Check stoploss only for trigger symbols
        ce_stoploss_hit = False
        pe_stoploss_hit = False
        ce_stoploss_trigger = None
        pe_stoploss_trigger = None

        # Only check stoploss for trigger symbols that also have sufficient range
        for sl_trigger_symbol in ce_trigger_symbols:
            if sl_trigger_symbol in self.market_data:
                ce_stoploss, _ = self._check_stoploss_hit(sl_trigger_symbol)
                if ce_stoploss:
                    ce_stoploss_hit = True
                    ce_stoploss_trigger = sl_trigger_symbol
                    logger.info(f"CE stoploss hit for trigger symbol {sl_trigger_symbol}")
                    break

        for sl_trigger_symbol in pe_trigger_symbols:
            if sl_trigger_symbol in self.market_data:
                _, pe_stoploss = self._check_stoploss_hit(sl_trigger_symbol)
                if pe_stoploss:
                    pe_stoploss_hit = True
                    pe_stoploss_trigger = sl_trigger_symbol
                    logger.info(f"PE stoploss hit for trigger symbol {sl_trigger_symbol}")
                    break

        # If stoploss hit for any trigger symbol, sell all positions of that type
        if ce_stoploss_hit:
            reason = f"Stoploss hit for trigger symbol {ce_stoploss_trigger} CE"
            logger.info(reason)
            for symbol in SYMBOLS:
                if self.positions[symbol]["CE"]["active"]:
                    self._sell_ce(symbol, reason)

        if pe_stoploss_hit:
            reason = f"Stoploss hit for trigger symbol {pe_stoploss_trigger} PE"
            logger.info(reason)
            for symbol in SYMBOLS:
                if self.positions[symbol]["PE"]["active"]:
                    self._sell_pe(symbol, reason)

        # Apply trading logic based on crossovers
        # Even though we only check for signals in symbols with sufficient range,
        # we still trade all symbols when a signal is triggered

        # Condition 5: Resistance crossed from below to above: Sell CE if active
        if resistance_crossed_above:
            reason = f"Resistance crossed from below to above in {trigger_symbol}"
            logger.info(reason)

            # Sell all active CE positions across all symbols
            for symbol in SYMBOLS:
                if self.positions[symbol]["CE"]["active"]:
                    self._sell_ce(symbol, reason)

        # Condition 6: Resistance crossed from above to below: Buy PE (replace CE if active)
        if resistance_crossed_below:
            reason = f"Resistance crossed from above to below in {trigger_symbol}"
            logger.info(reason)

            # Buy PE for all symbols (will automatically replace CE if active)
            for symbol in SYMBOLS:
                self._buy_pe(symbol, reason)

        # Condition 7: Support crossed from below to above: Buy CE (replace PE if active)
        if support_crossed_above:
            reason = f"Support crossed from below to above in {trigger_symbol}"
            logger.info(reason)

            # Buy CE for all symbols (will automatically replace PE if active)
            for symbol in SYMBOLS:
                self._buy_ce(symbol, reason)

        # Outbound Condition 1: Outbound support crossed from below to above: Buy CE (replace PE if active)
        if ce_outbound_signal:
            reason = f"Outbound support crossed from below to above in {outbound_trigger_symbol}"
            logger.info(reason)

            # Buy CE for all symbols (will automatically replace PE if active)
            for symbol in SYMBOLS:
                self._buy_ce(symbol, reason)

            # Reset outbound mode after processing
            self.outbound_mode = False
            self.outbound_direction = None
            self.outbound_trigger_time = None
            self.outbound_trigger_symbol = None
            self.stored_spot_prices = {symbol: None for symbol in SYMBOLS}
            self.outbound_trade_support = {symbol: None for symbol in SYMBOLS}
            self.outbound_trade_resistance = {symbol: None for symbol in SYMBOLS}

        # Outbound Condition 2: Outbound resistance crossed from above to below: Buy PE (replace CE if active)
        if pe_outbound_signal:
            reason = f"Outbound resistance crossed from above to below in {outbound_trigger_symbol}"
            logger.info(reason)

            # Buy PE for all symbols (will automatically replace CE if active)
            for symbol in SYMBOLS:
                self._buy_pe(symbol, reason)

            # Reset outbound mode after processing
            self.outbound_mode = False
            self.outbound_direction = None
            self.outbound_trigger_time = None
            self.outbound_trigger_symbol = None
            self.stored_spot_prices = {symbol: None for symbol in SYMBOLS}
            self.outbound_trade_support = {symbol: None for symbol in SYMBOLS}
            self.outbound_trade_resistance = {symbol: None for symbol in SYMBOLS}

        # Condition 8: Support crossed from above to below: Sell PE if active
        if support_crossed_below:
            reason = f"Support crossed from above to below in {trigger_symbol}"
            logger.info(reason)

            # Sell all active PE positions across all symbols
            for symbol in SYMBOLS:
                if self.positions[symbol]["PE"]["active"]:
                    self._sell_pe(symbol, reason)

    def _update_previous_values(self) -> None:
        """Update previous values for the next iteration."""
        for symbol in SYMBOLS:
            if symbol in self.market_data and self.market_data[symbol]:
                self.prev_spot_prices[symbol] = float(self.market_data[symbol]['Spot_LTP'])

        # Reset the outbound history update timestamp if it's been more than 3 seconds
        # This ensures we'll update the prev_outbound_spot_prices in the next iteration
        current_time = datetime.datetime.now()
        if (current_time - self.last_outbound_history_update).total_seconds() >= 3:
            self.last_outbound_history_update = current_time

    def run(self) -> None:
        """Run the trading algorithm with Zerodha integration."""
        logger.info("Starting LiveAlgoTrader with Zerodha integration")

        try:
            while True:
                # Check if market is open
                if not self._is_market_open():
                    logger.info("Market is closed. Waiting for market to open...")
                    time.sleep(60)  # Check every minute
                    continue

                # Fetch latest market data
                self._fetch_market_data()

                # Check if it's time to square off all positions
                if self._is_time_to_square_off():
                    self._square_off_all_positions()
                    logger.info("Trading day complete. Exiting...")
                    break

                # Process trading signals if within trading hours
                if self._is_trading_time():
                    self._process_trading_signals()
                else:
                    logger.info("Outside trading hours. Waiting...")

                # Update previous values for next iteration
                self._update_previous_values()

                # Sleep for a short time before next iteration
                time.sleep(1)  # Check every second

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt. Exiting...")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
        finally:
            # Square off all positions before exiting
            self._square_off_all_positions()

            # Close database connection
            if self.conn:
                self.conn.close()
                logger.info("Database connection closed")

            logger.info("LiveAlgoTrader stopped")

if __name__ == "__main__":
    trader = LiveAlgoTrader()
    trader.run()
