import os, json
import copy
import pandas as pd
import numpy as np
from kiteconnect import KiteConnect, KiteTicker
import time
import dateutil.parser
import threading
import sys
import requests
import urllib

from py_vollib.black_scholes.implied_volatility import implied_volatility
from py_vollib.black_scholes.greeks.analytical import delta, gamma, rho, theta, vega
import datetime
import configparser
import pymysql
import os

# Function to get MySQL connection details
def get_mysql_config():
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_data'
        }

# Get MySQL configuration
mysql_config = get_mysql_config()

# Connect to MySQL
conn = pymysql.connect(
    host=mysql_config['host'],
    port=int(mysql_config['port']),
    user=mysql_config['user'],
    password=mysql_config['password'],
    database=mysql_config['database']
)
cursor = conn.cursor()

# Create option_data_need_live_stocks table for stocks data only
cursor.execute("""
CREATE TABLE IF NOT EXISTS option_data_need_live_stocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp TEXT,
    symbol TEXT,
    Spot_LTP REAL,
    FUT_LTP REAL,
    VIX_LTP REAL,
    High REAL,
    Low REAL,
    Spot_LTP_Change REAL,
    FUT_LTP_Change REAL,
    VIX_LTP_Change REAL,

    FUT_OI INTEGER,
    FUT_Change_in_OI INTEGER,

    Total_Call_OI INTEGER,
    Total_Put_OI INTEGER,
    Total_Call_Change_in_OI INTEGER,
    Total_Put_Change_in_OI INTEGER,

    Max_Call_OI INTEGER,
    Max_Put_OI INTEGER,
    Max_Call_OI_Strike REAL,
    Max_Put_OI_Strike REAL,

    Max_Call_Change_in_OI INTEGER,
    Max_Put_Change_in_OI INTEGER,
    Max_Call_Change_in_OI_Strike REAL,
    Max_Put_Change_in_OI_Strike REAL,

    PCR REAL,
    Max_Pain_Strike REAL
)
""")
conn.commit()
print("Created option_data_need_live_stocks table for stocks data")

def get_login_credentials():
    global login_credential

    def login_credentials():
        print("---- Enter you Zerodha Login Credentials  ----")
        login_credential = {"api_key": str(input("Enter API Key : ").strip()),
                            "api_secret": str(input("Enter API Secret : ")).strip()}
        if input("Press Y to save login credential and any key to bypass : ").strip().upper() == "Y":
            with open(f"zerodha_login_details.json", "w") as f:
                json.dump(login_credential, f)
            print("Data Saved...")
        else:
            print("Data Save canceled!!!!!")

    while True:
        try:
            with open(f"zerodha_login_details.json", "r") as f:
                login_credential = json.load(f)
            break
        except:
            login_credentials()
    return login_credential

def get_access_token():
    global login_credential, access_token

    def login():
        global login_credential, access_token
        print("Trying Log In...")
        if login_credential["api_key"] == "TradeViaPython":
            print("Login url : ", "https://kite.zerodha.com ( Don't Login Anywhere else after this, Instead of mobile App. )")
            access_token = input("Login and enter your 'enctoken' here : ")
        else:
            kite = KiteConnect(api_key=login_credential["api_key"])
            print("Login url : ", kite.login_url())
            request_tkn = input("Login and enter your 'request_token' here : ")
            try:
                access_token = kite.generate_session(request_token=request_tkn, api_secret=login_credential["api_secret"])['access_token']
            except Exception as e:
                print(f"Login Failed {e}!!!!!")
        os.makedirs(f"AccessToken", exist_ok=True)
        with open(f"AccessToken/{datetime.datetime.now().date()}.json", "w") as f:
            json.dump(access_token, f)

    while True:
        if os.path.exists(f"AccessToken/{datetime.datetime.now().date()}.json"):
            with open(f"AccessToken/{datetime.datetime.now().date()}.json", "r") as f:
                access_token = json.load(f)
            break
        else:
            login()
    return access_token

def get_object():
    global kite, login_credential, access_token, user_id
    try:
        if login_credential["api_key"] == "TradeViaPython":
            class KiteApp:
                # Products
                PRODUCT_MIS = "MIS"
                PRODUCT_CNC = "CNC"
                PRODUCT_NRML = "NRML"
                PRODUCT_CO = "CO"

                # Order types
                ORDER_TYPE_MARKET = "MARKET"
                ORDER_TYPE_LIMIT = "LIMIT"
                ORDER_TYPE_SLM = "SL-M"
                ORDER_TYPE_SL = "SL"

                # Varities
                VARIETY_REGULAR = "regular"
                VARIETY_CO = "co"
                VARIETY_AMO = "amo"

                # Transaction type
                TRANSACTION_TYPE_BUY = "BUY"
                TRANSACTION_TYPE_SELL = "SELL"

                # Validity
                VALIDITY_DAY = "DAY"
                VALIDITY_IOC = "IOC"

                # Exchanges
                EXCHANGE_NSE = "NSE"
                EXCHANGE_BSE = "BSE"
                EXCHANGE_NFO = "NFO"
                EXCHANGE_CDS = "CDS"
                EXCHANGE_BFO = "BFO"
                EXCHANGE_MCX = "MCX"

                def __init__(self, enctoken):
                    self.enctoken = enctoken
                    self.headers = {"Authorization": f"enctoken {self.enctoken}"}
                    self.session = requests.session()
                    self.root_url = "https://kite.zerodha.com/oms"
                    self.session.get(self.root_url, headers=self.headers)

                def instruments(self, exchange=None):
                    data = self.session.get(f"https://api.kite.trade/instruments").text.split("\n")
                    Exchange = []
                    for i in data[1:-1]:
                        row = i.split(",")
                        if exchange is None or exchange == row[11]:
                            Exchange.append(
                                {'instrument_token': int(row[0]), 'exchange_token': row[1], 'tradingsymbol': row[2],
                                 'name': row[3][1:-1], 'last_price': float(row[4]),
                                 'expiry': dateutil.parser.parse(row[5]).date() if row[5] != "" else None,
                                 'strike': float(row[6]), 'tick_size': float(row[7]), 'lot_size': int(row[8]),
                                 'instrument_type': row[9], 'segment': row[10],
                                 'exchange': row[11]})
                    return Exchange

                def historical_data(self, instrument_token, from_date, to_date, interval, continuous=False, oi=False):
                    params = {"from": from_date,
                              "to": to_date,
                              "interval": interval,
                              "continuous": 1 if continuous else 0,
                              "oi": 1 if oi else 0}
                    lst = self.session.get(
                        f"{self.root_url}/instruments/historical/{instrument_token}/{interval}", params=params,
                        headers=self.headers).json()["data"]["candles"]
                    records = []
                    for i in lst:
                        record = {"date": dateutil.parser.parse(i[0]), "open": i[1], "high": i[2], "low": i[3],
                                  "close": i[4], "volume": i[5], }
                        if len(i) == 7:
                            record["oi"] = i[6]
                        records.append(record)
                    return records

                def margins(self):
                    margins = self.session.get(f"{self.root_url}/user/margins", headers=self.headers).json()["data"]
                    return margins

                def profile(self):
                    profile = self.session.get(f"{self.root_url}/user/profile", headers=self.headers).json()["data"]
                    return profile

                def orders(self):
                    orders = self.session.get(f"{self.root_url}/orders", headers=self.headers).json()["data"]
                    return orders

                def positions(self):
                    positions = self.session.get(f"{self.root_url}/portfolio/positions", headers=self.headers).json()[
                        "data"]
                    return positions

                def place_order(self, variety, exchange, tradingsymbol, transaction_type, quantity, product, order_type,
                                price=None,
                                validity=None, disclosed_quantity=None, trigger_price=None, squareoff=None,
                                stoploss=None,
                                trailing_stoploss=None, tag=None):
                    params = locals()
                    del params["self"]
                    for k in list(params.keys()):
                        if params[k] is None:
                            del params[k]
                    order_id = self.session.post(f"{self.root_url}/orders/{variety}",
                                                 data=params, headers=self.headers).json()["data"]["order_id"]
                    return order_id

                def modify_order(self, variety, order_id, parent_order_id=None, quantity=None, price=None,
                                 order_type=None,
                                 trigger_price=None, validity=None, disclosed_quantity=None):
                    params = locals()
                    del params["self"]
                    for k in list(params.keys()):
                        if params[k] is None:
                            del params[k]

                    order_id = self.session.put(f"{self.root_url}/orders/{variety}/{order_id}",
                                                data=params, headers=self.headers).json()["data"][
                        "order_id"]
                    return order_id

                def cancel_order(self, variety, order_id, parent_order_id=None):
                    order_id = self.session.delete(f"{self.root_url}/orders/{variety}/{order_id}",
                                                   data={"parent_order_id": parent_order_id} if parent_order_id else {},
                                                   headers=self.headers).json()["data"]["order_id"]
                    return order_id

            kite = KiteApp(enctoken=access_token)
        else:
            kite = KiteConnect(api_key=login_credential["api_key"], access_token=access_token)

        user_id = kite.profile()["user_id"]
        user_name = kite.profile()["user_name"]
        print(f"Logged In : {user_id} as {user_name}")
    except Exception as e:
        print(f"Login Error {e}!!!!!")
        os.remove(f"AccessToken/{datetime.datetime.now().date()}.json") if os.path.exists(
            f"AccessToken/{datetime.datetime.now().date()}.json") else None
        time.sleep(5)
        sys.exit()

def start_websocket():
    global login_credential, access_token, user_id, kws, tick_data, symbol_token, token_symbol
    access_token = access_token+"&user_id="+user_id if login_credential["api_key"] == "TradeViaPython" else access_token
    kws = KiteTicker(api_key=login_credential["api_key"], access_token=access_token)

    tick_data = {}
    token_symbol = {}

    def on_ticks(ws, ticks):
        for i in ticks:
            tick_data[token_symbol[i["instrument_token"]]] = i

    kws.on_ticks = on_ticks
    kws.connect(threaded=True)
    while not kws.is_connected():
        time.sleep(1)
    print("WebSocket : Connected")

def get_nfo_symbols():
    """Get all available NFO symbols"""
    exchange = None
    while True:
        if exchange is None:
            try:
                print("Downloading exchange data...")
                exchange = pd.DataFrame(kite.instruments())
                break
            except:
                print("Exchange Download Error...")
                time.sleep(10)

    # Get FNO symbols
    df = copy.deepcopy(exchange)
    df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
    nfo_symbols = list(df["name"].unique())
    nfo_symbols = [sym for sym in nfo_symbols if sym != "SENSEX50"]
    
    print(f"Found {len(nfo_symbols)} NFO symbols: {nfo_symbols}")
    return nfo_symbols, exchange

def get_oi(data):
    global prev_day_oi, kite, stop_thread
    for symbol, v in data.items():
        if stop_thread:
            break
        while True:
            try:
                prev_day_oi[symbol]
                break
            except:
                try:
                    pre_day_data = kite.historical_data(v["token"], (datetime.datetime.now() - datetime.timedelta(days=30)).date(),
                                          (datetime.datetime.now() - datetime.timedelta(days=1)).date(), "day", oi=True)
                    try:
                        prev_day_oi[symbol] = pre_day_data[-1]["oi"]
                    except:
                        prev_day_oi[symbol] = 0
                    break
                except Exception as e:
                    time.sleep(0.5)

# Define market hours
MARKET_START_TIME = datetime.time(9, 15)  # Market opens at 9:15 AM
MARKET_END_TIME = datetime.time(15, 30)  # Market closes at 3:30 PM

# Function to check if market is open
def is_market_open():
    now = datetime.datetime.now().time()
    return MARKET_START_TIME <= now <= MARKET_END_TIME

def process_symbol_data(inp_symbol, exchange):
    """Process option chain data for a single symbol"""
    global instrument_dict, prev_day_oi, stop_thread, tick_data, token_symbol, kws
    
    try:
        # Get first available expiry for options and futures
        df = copy.deepcopy(exchange)
        df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
        df = df[df["name"] == inp_symbol]
        
        # Get option expiry dates
        df_options = df[(df["instrument_type"] == "CE") | (df["instrument_type"] == "PE")]
        oc_expiries_list = sorted(list(df_options["expiry"].unique()))
        if not oc_expiries_list:
            print(f"No option expiries found for {inp_symbol}")
            return None
        inp_oc_expiry = oc_expiries_list[0]  # Use first available expiry
        
        # Get futures expiry dates
        df_futures = df[df["instrument_type"] == "FUT"]
        fut_expiries_list = sorted(list(df_futures["expiry"].unique()))
        if not fut_expiries_list:
            print(f"No futures expiries found for {inp_symbol}")
            return None
        inp_fut_expiry = fut_expiries_list[0]  # Use first available expiry
        
        inp_calc_base_fut = True  # Use futures as calculation base
        
        # Setup instruments for this symbol
        symbol_instrument_dict = {}
        
        # Options
        df = copy.deepcopy(exchange)
        df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
        df = df[df["name"] == inp_symbol]
        df = df[(df["instrument_type"] == "CE") | (df["instrument_type"] == "PE")]
        df = df[df["expiry"] == inp_oc_expiry]
        
        if df.empty:
            print(f"No option data found for {inp_symbol}")
            return None
            
        lot_size = list(df["lot_size"])[0]
        for i in df.index:
            symbol_key = f'{df["exchange"][i]}:{df["tradingsymbol"][i]}'
            symbol_instrument_dict[symbol_key] = {
                "strikePrice": float(df["strike"][i]),
                "instrumentType": df["instrument_type"][i],
                "token": df["instrument_token"][i]
            }
            token_symbol[int(df["instrument_token"][i])] = symbol_key
        
        # Futures
        df = copy.deepcopy(exchange)
        df = df[((df["exchange"] == "NFO") | (df["exchange"] == "BFO"))]
        df = df[df["name"] == inp_symbol]
        df = df[df["instrument_type"] == "FUT"]
        df = df[df["expiry"] == inp_fut_expiry]
        
        if df.empty:
            print(f"No futures data found for {inp_symbol}")
            return None
            
        for i in df.index:
            fut_instrument = f'{df["exchange"][i]}:{df["tradingsymbol"][i]}'
            symbol_instrument_dict[fut_instrument] = {
                "strikePrice": float(df["strike"][i]),
                "instrumentType": df["instrument_type"][i],
                "token": df["instrument_token"][i]
            }
            token_symbol[int(df["instrument_token"][i])] = fut_instrument
        
        # Add spot instrument
        index_map = {"NIFTY": "NSE:NIFTY 50", "BANKNIFTY": "NSE:NIFTY BANK", "FINNIFTY": "NSE:NIFTY FIN SERVICE",
                     "MIDCPNIFTY": "NSE:NIFTY MID SELECT", "SENSEX": "BSE:SENSEX", "BANKEX": "BSE:BANKEX",
                     "SENSEX50": "BSE:SENSEX50"}
        spot_instrument = index_map[inp_symbol] if inp_symbol in list(index_map) else f"NSE:{inp_symbol}"
        
        if spot_instrument not in list(token_symbol.values()):
            spot_df = copy.deepcopy(exchange)
            try:
                spot_token = list(spot_df[((spot_df["exchange"] == spot_instrument[:3]) & (spot_df["tradingsymbol"] == spot_instrument[4:]))]["instrument_token"])[0]
                token_symbol[int(spot_token)] = spot_instrument
            except:
                print(f"Could not find spot instrument for {inp_symbol}")
                return None
        
        # Add VIX
        if "NSE:INDIA VIX" not in list(token_symbol.values()):
            vix_df = copy.deepcopy(exchange)
            try:
                vix_token = list(vix_df[((vix_df["exchange"] == "NSE") & (vix_df["tradingsymbol"] == "INDIA VIX"))]["instrument_token"])[0]
                token_symbol[int(vix_token)] = "NSE:INDIA VIX"
            except:
                print("Could not find VIX instrument")
        
        # Subscribe to all tokens
        kws.subscribe(list(token_symbol.keys()))
        kws.set_mode(kws.MODE_FULL, list(token_symbol.keys()))
        time.sleep(1)
        
        # Get OI data in thread
        stop_thread = False
        thread = threading.Thread(target=get_oi, args=(symbol_instrument_dict,))
        thread.start()
        
        # Wait a bit for data to come
        time.sleep(3)
        
        # Process data
        option_data = {}
        fut_data = {}
        spot_data = {}
        vix_data = {}
        
        for symbol, values in tick_data.copy().items():
            if symbol == spot_instrument:
                spot_data = values
            elif symbol == "NSE:INDIA VIX":
                vix_data = values
            elif symbol == fut_instrument:
                fut_data = values
        
        # Check if we have required data
        if not spot_data or not fut_data or not vix_data:
            print(f"Missing required data for {inp_symbol}")
            return None
        
        for symbol, values in tick_data.copy().items():
            if symbol == spot_instrument or symbol == "NSE:INDIA VIX" or symbol == fut_instrument:
                pass
            else:
                if symbol in symbol_instrument_dict:
                    try:
                        option_data[symbol] = {}
                        option_data[symbol]["Strike_Price"] = symbol_instrument_dict[symbol]["strikePrice"]
                        option_data[symbol]["Instrument_Type"] = symbol_instrument_dict[symbol]["instrumentType"]
                        option_data[symbol]["LTP"] = values["last_price"]
                        option_data[symbol]["LTP_Change"] = values["last_price"] - values["ohlc"]["close"] if values["last_price"] != 0 else 0
                        option_data[symbol]["OI"] = int(values["oi"]/lot_size)
                        try:
                            option_data[symbol]["OI_Change"] = int((values["oi"] - prev_day_oi[symbol])/lot_size)
                        except:
                            option_data[symbol]["OI_Change"] = 0
                    except Exception as e:
                        continue
        
        if not option_data:
            print(f"No option data processed for {inp_symbol}")
            return None
        
        # Create dataframe and process
        df = pd.DataFrame(option_data).transpose()
        ce_df = df[df["Instrument_Type"] == "CE"]
        ce_df = ce_df.rename(columns={i: f"CE_{i}" for i in list(ce_df.keys())})
        ce_df.index = ce_df["CE_Strike_Price"]
        ce_df = ce_df.drop(["CE_Strike_Price"], axis=1)
        ce_df["Strike"] = ce_df.index

        pe_df = df[df["Instrument_Type"] == "PE"]
        pe_df = pe_df.rename(columns={i: f"PE_{i}" for i in list(pe_df.keys())})
        pe_df.index = pe_df["PE_Strike_Price"]
        pe_df = pe_df.drop("PE_Strike_Price", axis=1)
        df = pd.concat([ce_df, pe_df], axis=1).sort_index()
        df = df.replace(np.nan, 0)
        df["Strike"] = df.index
        
        # Calculate max pain
        total_profit_loss = {}
        for i in df.index:
            itm_call = df[df.index < i]
            itm_call_loss = (i - itm_call.index) * itm_call["CE_OI"]
            itm_put = df[df.index > i]
            itm_put_loss = (itm_put.index - i) * itm_put["PE_OI"]
            total_profit_loss[sum(itm_call_loss) + sum(itm_put_loss)] = i
        
        try:
            fut_change_oi = fut_data["oi"] - prev_day_oi[fut_instrument]
        except:
            fut_change_oi = 0
        
        # Create market data record
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        record = (
            timestamp,
            spot_data["last_price"],
            fut_data["last_price"],
            vix_data["last_price"],
            spot_data["ohlc"]["high"],
            spot_data["ohlc"]["low"],
            spot_data["last_price"] - spot_data["ohlc"]["close"],
            fut_data["last_price"] - fut_data["ohlc"]["close"],
            vix_data["last_price"] - vix_data["ohlc"]["close"],
            fut_data["oi"],
            fut_change_oi,
            sum(df["CE_OI"]),
            sum(df["PE_OI"]),
            sum(df["CE_OI_Change"]),
            sum(df["PE_OI_Change"]),
            max(df["CE_OI"]) if len(df["CE_OI"]) > 0 else 0,
            max(df["PE_OI"]) if len(df["PE_OI"]) > 0 else 0,
            df[df["CE_OI"] == max(df["CE_OI"])]["Strike"].iloc[0] if len(df["CE_OI"]) > 0 and max(df["CE_OI"]) > 0 else 0,
            df[df["PE_OI"] == max(df["PE_OI"])]["Strike"].iloc[0] if len(df["PE_OI"]) > 0 and max(df["PE_OI"]) > 0 else 0,
            max(df["CE_OI_Change"]) if len(df["CE_OI_Change"]) > 0 else 0,
            max(df["PE_OI_Change"]) if len(df["PE_OI_Change"]) > 0 else 0,
            df[df["CE_OI_Change"] == max(df["CE_OI_Change"])]["Strike"].iloc[0] if len(df["CE_OI_Change"]) > 0 and max(df["CE_OI_Change"]) > 0 else 0,
            df[df["PE_OI_Change"] == max(df["PE_OI_Change"])]["Strike"].iloc[0] if len(df["PE_OI_Change"]) > 0 and max(df["PE_OI_Change"]) > 0 else 0,
            round((sum(df["PE_OI"]) / sum(df["CE_OI"])) if sum(df["CE_OI"]) != 0 else 0, 2),
            total_profit_loss[min(total_profit_loss.keys())] if total_profit_loss else 0
        )
        
        # Add symbol to the record
        record_with_symbol = (record[0], inp_symbol) + record[1:]
        
        return record_with_symbol
        
    except Exception as e:
        print(f"Error processing {inp_symbol}: {e}")
        return None

# Initialize
get_login_credentials()
get_access_token()
get_object()
start_websocket()

print("----Option Chain for All Stocks----")
print("Processing all NFO symbols in rotation")

# Get all NFO symbols
nfo_symbols, exchange = get_nfo_symbols()

# Initialize variables
instrument_dict = {}
prev_day_oi = {}
stop_thread = False
tick_data = {}
token_symbol = {}

print(f"Starting processing loop for {len(nfo_symbols)} symbols during market hours")

current_symbol_index = 0

# Main loop
while True:
    # Check if market is open
    # if not is_market_open():
    #     current_time = datetime.datetime.now().strftime("%H:%M:%S")
    #     print(f"[{current_time}] Market is closed. Waiting for market hours (9:15 AM - 3:30 PM)...")
    #     time.sleep(60)  # Check every minute when market is closed
    #     continue
    
    # Get current symbol to process
    current_symbol = nfo_symbols[current_symbol_index]
    
    start_time = time.time()
    
    print(f"Processing symbol: {current_symbol} ({current_symbol_index + 1}/{len(nfo_symbols)})")
    
    # Clear previous subscriptions
    if token_symbol:
        try:
            kws.unsubscribe(list(token_symbol.keys()))
            time.sleep(1)
        except:
            pass
        tick_data = {}
        token_symbol = {}
    
    # Process current symbol
    record_with_symbol = process_symbol_data(current_symbol, exchange)
    
    if record_with_symbol:
        try:
            # Check if there's already a record for this symbol in option_data_need_live_stocks
            cursor.execute("SELECT id FROM option_data_need_live_stocks WHERE symbol = %s", (current_symbol,))
            existing_record = cursor.fetchone()

            if existing_record:
                # Update existing record
                cursor.execute("""
                    UPDATE option_data_need_live_stocks SET
                        timestamp = %s, Spot_LTP = %s, FUT_LTP = %s, VIX_LTP = %s, High = %s, Low = %s,
                        Spot_LTP_Change = %s, FUT_LTP_Change = %s, VIX_LTP_Change = %s,
                        FUT_OI = %s, FUT_Change_in_OI = %s,
                        Total_Call_OI = %s, Total_Put_OI = %s, Total_Call_Change_in_OI = %s, Total_Put_Change_in_OI = %s,
                        Max_Call_OI = %s, Max_Put_OI = %s, Max_Call_OI_Strike = %s, Max_Put_OI_Strike = %s,
                        Max_Call_Change_in_OI = %s, Max_Put_Change_in_OI = %s, Max_Call_Change_in_OI_Strike = %s, Max_Put_Change_in_OI_Strike = %s,
                        PCR = %s, Max_Pain_Strike = %s
                    WHERE symbol = %s
                """, (record_with_symbol[0],) + record_with_symbol[2:] + (current_symbol,))
                print(f"Updated live data for symbol {current_symbol} in option_data_need_live_stocks")
            else:
                # Insert new record
                cursor.execute("""
                    INSERT INTO option_data_need_live_stocks (
                        timestamp, symbol, Spot_LTP, FUT_LTP, VIX_LTP, High, Low,
                        Spot_LTP_Change, FUT_LTP_Change, VIX_LTP_Change,
                        FUT_OI, FUT_Change_in_OI,
                        Total_Call_OI, Total_Put_OI, Total_Call_Change_in_OI, Total_Put_Change_in_OI,
                        Max_Call_OI, Max_Put_OI, Max_Call_OI_Strike, Max_Put_OI_Strike,
                        Max_Call_Change_in_OI, Max_Put_Change_in_OI, Max_Call_Change_in_OI_Strike, Max_Put_Change_in_OI_Strike,
                        PCR, Max_Pain_Strike
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, record_with_symbol)
                print(f"Inserted new live data for symbol {current_symbol} in option_data_need_live_stocks")

            conn.commit()
            
        except Exception as e:
            print(f"Database error for {current_symbol}: {e}")
    else:
        print(f"No data processed for {current_symbol}")
    
    # Move to next symbol
    current_symbol_index = (current_symbol_index + 1) % len(nfo_symbols)
    
    # Calculate how much time to sleep to ensure we process one symbol per second
    elapsed_time = time.time() - start_time
    sleep_time = max(0, 1.0 - elapsed_time)  # Ensure we don't sleep for negative time
    if sleep_time > 0:
        time.sleep(sleep_time)