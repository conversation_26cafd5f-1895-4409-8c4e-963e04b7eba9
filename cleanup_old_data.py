#!/usr/bin/env python3
"""
Database Cleanup Script

This script deletes data older than 2 days from option_data and option_data_need tables
to maintain database performance and reduce storage requirements.

Usage:
    python cleanup_old_data.py
"""

import pymysql
import datetime
import logging
import configparser
import os
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cleanup_log.txt"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def get_mysql_config():
    """
    Get MySQL connection details from config file or use defaults.
    """
    config = configparser.ConfigParser()
    if os.path.exists('db_config.ini'):
        config.read('db_config.ini')
        return config['mysql']
    else:
        # Default configuration
        return {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': 'vinayak123',
            'database': 'option_chain_data'
        }

def get_db_connection():
    """
    Create and return a database connection.
    """
    mysql_config = get_mysql_config()
    try:
        conn = pymysql.connect(
            host=mysql_config['host'],
            port=int(mysql_config['port']),
            user=mysql_config['user'],
            password=mysql_config['password'],
            database=mysql_config['database']
        )
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        sys.exit(1)

def cleanup_old_data():
    """
    Delete data older than 2 days from option_data and option_data_need tables.
    """
    # Calculate the date threshold (2 days ago)
    threshold_date = datetime.datetime.now() - datetime.timedelta(days=2)
    threshold_str = threshold_date.strftime('%Y-%m-%d')
    
    logger.info(f"Starting cleanup process. Deleting data older than {threshold_str}")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # Get count of records before deletion for reporting
        cursor.execute("SELECT COUNT(*) FROM option_data")
        option_data_count_before = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM option_data_need")
        option_data_need_count_before = cursor.fetchone()[0]
        
        # Delete from option_data table
        cursor.execute(
            "DELETE FROM option_data WHERE DATE(timestamp) < %s",
            (threshold_str,)
        )
        option_data_deleted = cursor.rowcount
        
        # Delete from option_data_need table
        cursor.execute(
            "DELETE FROM option_data_need WHERE DATE(timestamp) < %s",
            (threshold_str,)
        )
        option_data_need_deleted = cursor.rowcount
        
        # Commit the changes
        conn.commit()
        
        # Get count of records after deletion
        cursor.execute("SELECT COUNT(*) FROM option_data")
        option_data_count_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM option_data_need")
        option_data_need_count_after = cursor.fetchone()[0]
        
        # Log the results
        logger.info(f"option_data: {option_data_deleted} records deleted")
        logger.info(f"option_data_need: {option_data_need_deleted} records deleted")
        logger.info(f"option_data: {option_data_count_before} → {option_data_count_after} records")
        logger.info(f"option_data_need: {option_data_need_count_before} → {option_data_need_count_after} records")
        
        # Optional: Optimize tables after large deletions
        logger.info("Optimizing tables...")
        cursor.execute("OPTIMIZE TABLE option_data")
        cursor.execute("OPTIMIZE TABLE option_data_need")
        
        logger.info("Cleanup completed successfully")
        
    except Exception as e:
        conn.rollback()
        logger.error(f"Error during cleanup: {e}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    logger.info("Database cleanup script started")
    cleanup_old_data()
    logger.info("Database cleanup script finished")
