import pyotp

def get_access_token():
    global login_credential, access_token

    def login():
        global login_credential, access_token
        print("Trying Log In...")

        print("Login url : ", "https://kite.zerodha.com ( Don't Login Anywhere else after this, Instead of mobile App. )")
        #access_token = input("Login and enter your 'enctoken' here : ")
        token =  'IDJZYPHLUYZMHNRACK2V3KBKVQAQJVRW'                         # Enter Totp
        totp = pyotp.TOTP(token).now()
        #print("Totp is : "+totp)

        # # First Way to Login
        # # You can use your Kite app in mobile
        # # But You can't login anywhere in 'kite.zerodha.com' website else this session will disconnected

        user_id = "PXZ258"       # Login Id
        password = "shiv@331$"      # Login password
        twofa = totp         # Login Pin or TOTP nothing to enter

        access_token= get_enctoken(user_id, password, twofa)
        os.makedirs(f"AccessToken", exist_ok=True)
        print(access_token)
        with open(f"AccessToken/{datetime.datetime.now().date()}.json", "w") as f:
            json.dump(access_token, f)

    while True:
        if os.path.exists(f"AccessToken/{datetime.datetime.now().date()}.json"):
            with open(f"AccessToken/{datetime.datetime.now().date()}.json", "r") as f:
                access_token = json.load(f)
            break
        else:
            login()
    return access_token

def get_enctoken(userid, password, twofa):
    session = requests.Session()
    response = session.post('https://kite.zerodha.com/api/login', data={
        "user_id": userid,
        "password": password
    })
    response = session.post('https://kite.zerodha.com/api/twofa', data={
        "request_id": response.json()['data']['request_id'],
        "twofa_value": twofa,
        "user_id": response.json()['data']['user_id']
    })
    enctoken = response.cookies.get('enctoken')
    if enctoken:
        return enctoken
    else:
        raise Exception("Enter valid details !!!!")
get_access_token()