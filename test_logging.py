import logging
import sys

# Reset root logger to avoid conflicts with other scripts
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

# Create a specific logger for testing
logger = logging.getLogger("TestLogger")
logger.setLevel(logging.INFO)

# Create handlers
file_handler = logging.FileHandler("test_logging.log")
console_handler = logging.StreamHandler(sys.stdout)

# Create formatters and add it to handlers
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Add handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Force a test log message to verify logging is working
logger.info("Test logger initialized")
logger.warning("This is a warning message")
logger.error("This is an error message")

print("Logging test complete. Check test_logging.log file.")
